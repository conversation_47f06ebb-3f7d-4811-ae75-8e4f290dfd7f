'use client'

import { useSession, signIn, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestAuthPage() {
  const { data: session, status } = useSession()

  const handleTestLogin = async () => {
    try {
      const result = await signIn('credentials', {
        email: '<EMAIL>',
        password: 'admin123456',
        redirect: false
      })
      console.log('登录结果:', result)
    } catch (error) {
      console.error('登录错误:', error)
    }
  }

  const handleTestRegister = async () => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: '测试用户',
          email: '<EMAIL>',
          password: 'Test123456'
        })
      })
      
      const result = await response.json()
      console.log('注册结果:', result)
    } catch (error) {
      console.error('注册错误:', error)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>认证功能测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">当前状态</h3>
            <p>状态: {status}</p>
            {session ? (
              <div>
                <p>用户: {session.user?.name}</p>
                <p>邮箱: {session.user?.email}</p>
                <p>角色: {session.user?.roles?.join(', ')}</p>
                <p>会员等级: {session.user?.membershipLevel}</p>
              </div>
            ) : (
              <p>未登录</p>
            )}
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">测试操作</h3>
            
            <Button 
              onClick={handleTestLogin}
              className="w-full"
              disabled={status === 'loading'}
            >
              测试登录 (<EMAIL>)
            </Button>
            
            <Button 
              onClick={handleTestRegister}
              variant="outline"
              className="w-full"
              disabled={status === 'loading'}
            >
              测试注册 (<EMAIL>)
            </Button>
            
            {session && (
              <Button 
                onClick={() => signOut()}
                variant="destructive"
                className="w-full"
              >
                退出登录
              </Button>
            )}
          </div>

          <div className="text-sm text-gray-500">
            <p>打开浏览器开发者工具查看详细日志</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
