'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowUpRight, ArrowDownLeft, Gift, RefreshCw, ExternalLink } from 'lucide-react'
import { Transaction, TransactionType, TransactionStatus } from '@prisma/client'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import Link from 'next/link'

interface RecentTransactionsProps {
  transactions: Transaction[]
}

export function RecentTransactions({ transactions }: RecentTransactionsProps) {
  const getTransactionIcon = (type: TransactionType) => {
    switch (type) {
      case 'RECHARGE':
        return ArrowDownLeft
      case 'CONSUME':
        return ArrowUpRight
      case 'REWARD':
        return Gift
      case 'REFUND':
        return RefreshCw
      default:
        return ArrowUpRight
    }
  }

  const getTransactionColor = (type: TransactionType) => {
    switch (type) {
      case 'RECHARGE':
        return 'text-green-600'
      case 'CONSUME':
        return 'text-red-600'
      case 'REWARD':
        return 'text-blue-600'
      case 'REFUND':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  const getTransactionLabel = (type: TransactionType) => {
    switch (type) {
      case 'RECHARGE':
        return '充值'
      case 'CONSUME':
        return '消费'
      case 'REWARD':
        return '奖励'
      case 'REFUND':
        return '退款'
      case 'TRANSFER':
        return '转账'
      default:
        return '未知'
    }
  }

  const getStatusBadge = (status: TransactionStatus) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge variant="default" className="bg-green-100 text-green-800">已完成</Badge>
      case 'PENDING':
        return <Badge variant="secondary">待处理</Badge>
      case 'FAILED':
        return <Badge variant="destructive">失败</Badge>
      case 'CANCELLED':
        return <Badge variant="outline">已取消</Badge>
      default:
        return <Badge variant="secondary">未知</Badge>
    }
  }

  if (transactions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            最近交易
            <Button variant="outline" size="sm" asChild>
              <Link href="/transactions">
                查看全部
                <ExternalLink className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <ArrowUpRight className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无交易记录</h3>
            <p className="text-gray-600 mb-4">
              您还没有任何交易记录，开始使用功能来获得欢乐豆吧！
            </p>
            <Button asChild>
              <Link href="/tools">
                开始使用工具
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          最近交易
          <Button variant="outline" size="sm" asChild>
            <Link href="/transactions">
              查看全部
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.map((transaction) => {
            const Icon = getTransactionIcon(transaction.type)
            const color = getTransactionColor(transaction.type)
            const isIncome = transaction.type === 'RECHARGE' || transaction.type === 'REWARD'
            
            return (
              <div key={transaction.id} className="flex items-center justify-between p-3 rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-gray-50`}>
                    <Icon className={`h-4 w-4 ${color}`} />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {transaction.description}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-gray-500">
                        {getTransactionLabel(transaction.type)}
                      </span>
                      <span className="text-xs text-gray-400">•</span>
                      <span className="text-xs text-gray-500">
                        {formatDistanceToNow(new Date(transaction.createdAt), {
                          addSuffix: true,
                          locale: zhCN
                        })}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <p className={`font-semibold ${isIncome ? 'text-green-600' : 'text-red-600'}`}>
                      {isIncome ? '+' : '-'}{Number(transaction.amount).toLocaleString()}
                    </p>
                    <div className="mt-1">
                      {getStatusBadge(transaction.status)}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
