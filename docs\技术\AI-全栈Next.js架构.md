全栈 Next.js 架构 方案实现下面的功能
## 核心功能要求
1. **用户认证系统**
    - 注册、登录、密码重置功能
    - JWT token 管理
    - 安全性措施（密码加密、防暴力破解等）

2. **三级会员系统**
    - 游客用户：使用 FingerprintJS 库进行设备指纹识别和追踪
    - 黄金用户：付费用户，享有中级权限
    - 钻石用户：高级付费用户，享有最高权限

3. **虚拟货币系统**
    - "欢乐豆"作为虚拟货币
    - 用户通过购买获得欢乐豆
    - 使用装备强化模拟器等功能需要消耗欢乐豆
    - 需要实现余额检查、消费记录、充值功能

4. ** api接口 **
    - 装备强化接口(星力、潜能、附加属性三种类型)、
    - 

给出具体实现的技术栈和技术方案。给出所有的技术细节。
如果保留当前的SSG方式（output: 'export'）。需要修改哪些文件？给出具体的修改或迁移方案。
将你的分析都行写入本地md文件。文件格式是md。不需要写代码。



### rewrite
基于当前的冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），使用全栈 Next.js 架构方案实现以下完整功能系统，并提供详细的技术实施方案：

## 核心功能要求

1. **用户认证系统**
    - 用户注册、登录、密码重置完整流程
    - JWT token 管理（包括 access token 和 refresh token 机制）
    - 安全性措施：密码加密存储、防暴力破解、会话管理、CSRF防护
    - 邮箱验证和账户激活流程

2. **三级会员系统**
    - 游客用户：使用 FingerprintJS 库进行设备指纹识别和追踪，限制功能使用
    - 黄金用户：付费用户，享有中级权限（如高级模拟器功能、数据导出等）
    - 钻石用户：高级付费用户，享有最高权限（如无限制使用、优先支持等）
    - 基于角色的权限控制（RBAC）系统实现

3. **虚拟货币系统（"欢乐豆"）**
    - 虚拟货币的获取：用户充值购买、签到奖励、活动赠送
    - 消费场景：装备强化模拟器使用、高级功能解锁、数据导出等
    - 核心功能：余额查询、消费记录、充值历史、交易日志
    - 安全机制：防刷币、交易验证、余额保护

4. **API接口系统**
    - 装备强化相关接口：
        * 星之力强化接口（支持不同星级的成功率计算）
        * 潜能重设接口（支持不同等级魔方的概率计算）
        * 附加属性强化接口（支持不同类型的属性提升）
    - 用户管理接口：注册、登录、权限验证、会员状态查询
    - 虚拟货币接口：余额查询、消费扣除、充值记录、交易历史
    - 数据管理接口：用户配置保存、历史记录查询、统计数据

## 技术实施要求

1. **技术栈选择和架构设计**
    - 详细说明全栈 Next.js 14 的技术栈组合（包括数据库、缓存、认证库等）
    - 项目目录结构重组方案
    - API Routes 的设计模式和最佳实践

2. **SSG配置兼容性分析**
    - 分析当前 SSG 配置（output: 'export'）与动态API功能的兼容性
    - 提供具体的配置修改方案：哪些文件需要修改、如何修改
    - 混合部署策略：哪些页面保持SSG，哪些需要SSR或客户端渲染
    - 迁移路径：从纯SSG到混合架构的具体步骤

3. **数据库设计和数据管理**
    - 用户表、角色表、权限表的详细设计
    - 虚拟货币相关表结构（余额表、交易记录表等）
    - 数据库选择建议（考虑Next.js生态和部署便利性）
    - 数据迁移和备份策略
    - mysql、mongodb，还是其他？ 给出一个对比和评估

4. **安全性和性能考虑**
    - JWT token 的安全存储和管理策略
    - API 接口的安全防护措施
    - 虚拟货币系统的安全机制设计
    - 性能优化方案（缓存策略、数据库优化等）

5. **部署和运维方案**
    - 与现有SSG部署的兼容性
    - 推荐的托管平台和部署策略
    - 环境变量和配置管理
    - 监控和日志方案

## 输出要求

将完整的技术分析和实施方案写入本地markdown文件，文件命名为"全栈Next.js架构实施方案.md"，包含：
- 详细的技术栈选择理由和对比
- 完整的项目架构设计
- 具体的文件修改和迁移方案
- 分阶段的实施计划
- 风险评估和解决方案

注意：只需要提供技术方案和架构设计，不需要编写具体的代码实现。重点关注如何在保持现有SSG优势的同时，集成动态后端功能。


基于当前的冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），使用全栈 Next.js 架构方案实现以下完整功能系统，并提供详细的技术实施方案：

## 核心功能要求

1. **用户认证系统**
    - 用户注册、登录、密码重置完整流程
    - JWT token 管理（包括 access token 和 refresh token 机制）
    - 安全性措施：密码加密存储、防暴力破解、会话管理、CSRF防护
    - 邮箱验证和账户激活流程

2. **三级会员系统**
    - 游客用户：使用 FingerprintJS 库进行设备指纹识别和追踪，限制功能使用
    - 黄金用户：付费用户，享有中级权限（如高级模拟器功能、数据导出等）
    - 钻石用户：高级付费用户，享有最高权限（如无限制使用、优先支持等）
    - 基于角色的权限控制（RBAC）系统实现

3. **虚拟货币系统（"欢乐豆"）**
    - 虚拟货币的获取：用户充值购买、签到奖励、活动赠送
    - 消费场景：装备强化模拟器使用、高级功能解锁、数据导出等
    - 核心功能：余额查询、消费记录、充值历史、交易日志
    - 安全机制：防刷币、交易验证、余额保护

4. **API接口系统**
    - 装备强化相关接口：
        * 星之力强化接口（支持不同星级的成功率计算）
        * 潜能重设接口（支持不同等级魔方的概率计算）
        * 附加属性强化接口（支持不同类型的属性提升）
    - 用户管理接口：注册、登录、权限验证、会员状态查询
    - 虚拟货币接口：余额查询、消费扣除、充值记录、交易历史
    - 数据管理接口：用户配置保存、历史记录查询、统计数据

## 技术实施要求

1. **技术栈选择和架构设计**
    - 详细说明全栈 Next.js 14 的技术栈组合（包括数据库、缓存、认证库等）
    - 项目目录结构重组方案
    - API Routes 的设计模式和最佳实践

2. **SSG配置兼容性分析**
    - 分析当前 SSG 配置（output: 'export'）与动态API功能的兼容性
    - 提供具体的配置修改方案：哪些文件需要修改、如何修改
    - 混合部署策略：哪些页面保持SSG，哪些需要SSR或客户端渲染
    - 迁移路径：从纯SSG到混合架构的具体步骤

3. **数据库设计和数据管理**
    - 用户表、角色表、权限表的详细设计
    - 虚拟货币相关表结构（余额表、交易记录表等）
    - 数据库选择建议（考虑Next.js生态和部署便利性）
    - 数据迁移和备份策略
    - MySQL、MongoDB、PostgreSQL等数据库方案的详细对比和评估

4. **安全性和性能考虑**
    - JWT token 的安全存储和管理策略
    - API 接口的安全防护措施
    - 虚拟货币系统的安全机制设计
    - 性能优化方案（缓存策略、数据库优化等）

5. **部署和运维方案**
    - 与现有SSG部署的兼容性
    - 推荐的托管平台和部署策略
    - 环境变量和配置管理
    - 监控和日志方案

## 输出要求

将完整的技术分析和实施方案写入本地markdown文件，文件命名为"全栈Next.js架构实施方案.md"，包含：
- 详细的技术栈选择理由和对比
- 完整的项目架构设计
- 具体的文件修改和迁移方案
- 分阶段的实施计划
- 风险评估和解决方案

注意：只需要提供技术方案和架构设计，不需要编写具体的代码实现。重点关注如何在保持现有SSG优势的同时，集成动态后端功能。
