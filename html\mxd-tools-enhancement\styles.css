/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Loading界面样式 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 50%, #0f0f23 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-container {
    text-align: center;
    color: #ffffff;
    max-width: 600px;
    width: 90%;
}

.loading-logo h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffd700, #ffb700, #ffd700);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 2s ease-in-out infinite;
}

.loading-subtitle {
    font-size: 0.9rem;
    color: #cccccc;
    margin-bottom: 3rem;
    font-style: italic;
}

@keyframes shimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.loading-progress {
    margin-bottom: 3rem;
}

.progress-bar-container {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ff88, #00ddff, #0099ff);
    background-size: 200% 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
    animation: progressShine 2s linear infinite;
    width: 0%;
}

@keyframes progressShine {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.progress-text {
    font-size: 1rem;
    color: #ffffff;
    font-weight: 500;
}

.loading-status {
    font-size: 0.9rem;
    color: #aaaaaa;
    min-height: 1.5rem;
}

.loading-tips {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: left;
}

.tip-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 1rem;
    text-align: center;
}

.tip-content {
    font-size: 0.85rem;
    line-height: 1.6;
}

.tip-item {
    color: #cccccc;
    margin-bottom: 0.5rem;
    padding-left: 0.5rem;
}

.tip-item:last-child {
    margin-bottom: 0;
}

/* 强制移除所有发光效果 */
*, *:hover, *:active, *:focus {
    filter: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
    outline: none !important;
}

/* 特别针对按钮元素 */
button, input[type="button"], .guide-button, .close-button, 
.tab-starforce, .tab-potential, .tab-bonusstat,
.enhance-button, .cancel-button, .stop-btn, .popup-btn, .result-btn {
    filter: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
    outline: none !important;
}

button:hover, input[type="button"]:hover, .guide-button:hover, .close-button:hover,
.tab-starforce:hover, .tab-potential:hover, .tab-bonusstat:hover,
.enhance-button:hover, .cancel-button:hover, .stop-btn:hover, .popup-btn:hover, .result-btn:hover {
    filter: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
    outline: none !important;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background: #000;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    color: #ffffff;
}

#app {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 主界面容器 - 按照XML尺寸507x712 */
.ui-container {
    position: relative;
    width: 507px;
    height: 712px;
    margin: 0 auto;
    overflow: visible;
}

/* 道具选择列表框 */
.item-selector-panel {
    position: absolute;
    left: -600px;
    top: 50px;
    width: 260px;
    height: 600px;
    background: rgba(20, 20, 40, 0.95);
    border: 2px solid rgba(100, 150, 255, 0.3);
    border-radius: 10px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    z-index: 1;
    display: block !important; /* 强制显示 */
    visibility: visible !important; /* 强制可见 */
}

.item-selector-header {
    padding: 15px;
    border-bottom: 1px solid rgba(100, 150, 255, 0.2);
    background: rgba(50, 50, 100, 0.3);
    border-radius: 8px 8px 0 0;
}

.item-selector-header h3 {
    color: #ffffff;
    font-size: 16px;
    margin: 0 0 5px 0;
    font-weight: bold;
}

.item-filter-info {
    color: #88ccff;
    font-size: 12px;
    font-style: italic;
}

.item-selector-content {
    padding: 10px;
    height: calc(100% - 80px);
    overflow-y: auto;
}

.item-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 6px;
    max-height: 100%;
}

.item-icon {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(100, 150, 255, 0.3);
    border-radius: 6px;
    background: rgba(30, 30, 60, 0.8);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;
}

.item-icon:hover {
    border-color: rgba(255, 215, 0, 0.8);
    background: rgba(50, 50, 100, 0.9);
    transform: scale(1.1);
    z-index: 10;
}

.item-icon.selected {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.2);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.item-icon img {
    width: 32px;
    height: 32px;
    object-fit: contain;
}

.item-icon .item-level-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    background: rgba(255, 100, 100, 0.9);
    color: #ffffff;
    font-size: 9px;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: bold;
    min-width: 12px;
    text-align: center;
}

/* 道具详情提示框 */
.item-tooltip {
    position: absolute;
    left: 280px;
    top: 0;
    width: 250px;
    background: rgba(20, 20, 40, 0.98);
    border: 2px solid rgba(255, 215, 0, 0.6);
    border-radius: 8px;
    padding: 12px;
    z-index: 10000;
    display: none;
    pointer-events: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.7);
}

.tooltip-title {
    color: #ffd700;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
    padding-bottom: 4px;
}

.tooltip-level, .tooltip-category {
    font-size: 12px;
    color: #cccccc;
    margin-bottom: 8px;
}

/* 属性要求区域 */
.tooltip-requirements {
    background: rgba(50, 50, 100, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
    border-radius: 4px;
    padding: 8px;
    margin: 8px 0;
}

.requirement-item {
    font-size: 11px;
    color: #ccddff;
    margin-bottom: 3px;
    display: flex;
    justify-content: space-between;
}

.requirement-item:last-child {
    margin-bottom: 0;
}

.requirement-item.insufficient {
    color: #ff8888;
    font-weight: bold;
}

/* 现金道具状态样式 */
.tooltip-cash-item {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    text-align: center;
}

.tooltip-cash-item.normal-item {
    color: #88ff88;
    background: rgba(136, 255, 136, 0.1);
    border: 1px solid rgba(136, 255, 136, 0.3);
}

.tooltip-cash-item.cash-item {
    color: #ff8888;
    background: rgba(255, 136, 136, 0.1);
    border: 1px solid rgba(255, 136, 136, 0.3);
}

.tooltip-features {
    margin-top: 8px;
    border-top: 1px solid rgba(100, 150, 255, 0.3);
    padding-top: 8px;
}

.feature-item {
    color: #88ff88;
    font-size: 11px;
    margin-bottom: 3px;
}

.feature-item.disabled {
    color: #ff8888;
}

.feature-item.disabled::before {
    content: "✗ ";
}

.feature-item:not(.disabled)::before {
    content: "✓ ";
}

/* 滚动条样式 */
.item-selector-content::-webkit-scrollbar {
    width: 6px;
}

.item-selector-content::-webkit-scrollbar-track {
    background: rgba(50, 50, 100, 0.3);
    border-radius: 3px;
}

.item-selector-content::-webkit-scrollbar-thumb {
    background: rgba(100, 150, 255, 0.5);
    border-radius: 3px;
}

.item-selector-content::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 150, 255, 0.8);
}

/* Z-0: 主背景 */
.main-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 507px;
    height: 712px;
    background-image: url('UIEquipEnchant.img/Main/Background.png');
    background-size: 507px 712px;
    background-repeat: no-repeat;
    z-index: 0;
}

/* Z-1: 顶部按钮 */
.guide-button {
    position: absolute;
    top: 3px;
    right: 37px;
    width: 14px;
    height: 17px;
    background-image: url('UIEquipEnchant.img/Main/button_Guide/normal/0.png');
    background-size: 14px 17px;
    background-repeat: no-repeat;
    border: none;
    outline: none;
    box-shadow: none;
    filter: none;
    cursor: pointer;
    z-index: 100;
}

.guide-button:hover {
    background-image: url('UIEquipEnchant.img/Main/button_Guide/mouseOver/0.png');
    outline: none;
    box-shadow: none;
    filter: none;
}

.guide-button:active {
    background-image: url('UIEquipEnchant.img/Main/button_Guide/pressed/0.png');
    outline: none;
    box-shadow: none;
    filter: none;
}

.guide-button:focus {
    outline: none;
    box-shadow: none;
    filter: none;
}

.guide-button:disabled {
    background-image: url('UIEquipEnchant.img/Main/button_Guide/disabled/0.png');
    cursor: not-allowed;
    outline: none;
    box-shadow: none;
    filter: none;
}

.close-button {
    position: absolute;
    top: 4px;
    right: 22px;
    width: 17px;
    height: 17px;
    background-image: url('UIEquipEnchant.img/Main/button_Close/normal/0.png');
    background-size: 17px 17px;
    background-repeat: no-repeat;
    border: none;
    outline: none;
    box-shadow: none;
    filter: none;
    cursor: pointer;
    z-index: 100;
}

.close-button:hover {
    background-image: url('UIEquipEnchant.img/Main/button_Close/mouseOver/0.png');
    outline: none;
    box-shadow: none;
    filter: none;
}

.close-button:active {
    background-image: url('UIEquipEnchant.img/Main/button_Close/pressed/0.png');
    outline: none;
    box-shadow: none;
    filter: none;
}

.close-button:focus {
    outline: none;
    box-shadow: none;
    filter: none;
}

.close-button:disabled {
    background-image: url('UIEquipEnchant.img/Main/button_Close/disabled/0.png');
    cursor: not-allowed;
    outline: none;
    box-shadow: none;
    filter: none;
}

/* === 星力强化标签页 === */
.tab-starforce {
    position: absolute;
    top: 81px;
    left: 20px;
    width: 158px;
    height: 33px;
    background-size: 158px 33px;
    background-repeat: no-repeat;
    border: none;
    outline: none;
    box-shadow: none;
    filter: none;
    cursor: pointer;
    z-index: 100;
    transition: all 0.15s ease;
}

/* 可选择状态（默认） */
.tab-starforce:not(.active):not(.disabled) {
    background-image: url('UIEquipEnchant.img/Main/Tab/Starforce/normal/0.png');
}

/* 鼠标进入状态 */
.tab-starforce:not(.active):not(.disabled):hover {
    background-image: url('UIEquipEnchant.img/Main/Tab/Starforce/normal/0.png');
    filter: brightness(1.1) contrast(1.05);
    transform: translateY(-1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 激活状态 */
.tab-starforce.active {
    background-image: url('UIEquipEnchant.img/Main/Tab/Starforce/selected/0.png');
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 禁止状态 */
.tab-starforce.disabled {
    background-image: url('UIEquipEnchant.img/Main/Tab/Starforce/disabled/0.png');
    cursor: not-allowed;
    opacity: 0.5;
    pointer-events: none;
    filter: grayscale(0.3);
}

.tab-starforce:focus {
    outline: none;
    box-shadow: none;
    filter: none;
}

/* 按压效果 */
.tab-starforce:not(.disabled):active {
    transform: translateY(1px);
    filter: brightness(0.9);
}

/* === 潜能重设标签页 === */
.tab-potential {
    position: absolute;
    top: 81px;
    left: 178px;
    width: 158px;
    height: 33px;
    background-size: 158px 33px;
    background-repeat: no-repeat;
    border: none;
    outline: none;
    box-shadow: none;
    filter: none;
    cursor: pointer;
    z-index: 100;
    transition: all 0.15s ease;
}

/* 可选择状态（默认） */
.tab-potential:not(.active):not(.disabled) {
    background-image: url('UIEquipEnchant.img/Main/Tab/Potential/normal/0.png');
}

/* 鼠标进入状态 */
.tab-potential:not(.active):not(.disabled):hover {
    background-image: url('UIEquipEnchant.img/Main/Tab/Potential/normal/0.png');
    filter: brightness(1.1) contrast(1.05);
    transform: translateY(-1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 激活状态 */
.tab-potential.active {
    background-image: url('UIEquipEnchant.img/Main/Tab/Potential/selected/0.png');
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 禁止状态 */
.tab-potential.disabled {
    background-image: url('UIEquipEnchant.img/Main/Tab/Potential/disabled/0.png');
    cursor: not-allowed;
    opacity: 0.5;
    pointer-events: none;
    filter: grayscale(0.3);
}

.tab-potential:focus {
    outline: none;
    box-shadow: none;
    filter: none;
}

/* 按压效果 */
.tab-potential:not(.disabled):active {
    transform: translateY(1px);
    filter: brightness(0.9);
}

/* === 额外属性标签页 === */
.tab-bonusstat {
    position: absolute;
    top: 81px;
    left: 336px;
    width: 158px;
    height: 33px;
    background-size: 158px 33px;
    background-repeat: no-repeat;
    border: none;
    outline: none;
    box-shadow: none;
    filter: none;
    cursor: pointer;
    z-index: 100;
    transition: all 0.15s ease;
}

/* 可选择状态（默认） */
.tab-bonusstat:not(.active):not(.disabled) {
    background-image: url('UIEquipEnchant.img/Main/Tab/Bonusstat/normal/0.png');
}

/* 鼠标进入状态 */
.tab-bonusstat:not(.active):not(.disabled):hover {
    background-image: url('UIEquipEnchant.img/Main/Tab/Bonusstat/normal/0.png');
    filter: brightness(1.1) contrast(1.05);
    transform: translateY(-1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 激活状态 */
.tab-bonusstat.active {
    background-image: url('UIEquipEnchant.img/Main/Tab/Bonusstat/selected/0.png');
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 禁止状态 */
.tab-bonusstat.disabled {
    background-image: url('UIEquipEnchant.img/Main/Tab/Bonusstat/disabled/0.png');
    cursor: not-allowed;
    opacity: 0.5;
    pointer-events: none;
    filter: grayscale(0.3);
}

.tab-bonusstat:focus {
    outline: none;
    box-shadow: none;
    filter: none;
}

/* 按压效果 */
.tab-bonusstat:not(.disabled):active {
    transform: translateY(1px);
    filter: brightness(0.9);
}

/* Z-1: Tab分割线 */
.tab-line-background {
    position: absolute;
    top: 112px;
    left: 7px;
    width: 482px;
    height: 2px;
    background-image: url('UIEquipEnchant.img/Main/Tab/Image_Line.png');
    background-size: 482px 2px;
    background-repeat: no-repeat;
    z-index: 100;
}

/* Z-2: 装备槽背景 */
.equip-slot-background {
    position: absolute;
    top: 168px;
    left: 209px;
    width: 90px;
    height: 90px;
    background-image: url('UIEquipEnchant.img/Main/Equip/Slot/Starforce.png');
    background-size: 90px 90px;
    background-repeat: no-repeat;
    cursor: pointer;
    z-index: 2;
}

/* 不同Tab的装备槽背景 */
.equip-slot-background.starforce { background-image: url('UIEquipEnchant.img/Main/Equip/Slot/Starforce.png'); }
.equip-slot-background.potential { background-image: url('UIEquipEnchant.img/Main/Equip/Slot/Potential.png'); }
.equip-slot-background.bonusstat { background-image: url('UIEquipEnchant.img/Main/Equip/Slot/Bonusstat.png'); }

/* Z-3: 装备徽章 */
.equip-badge {
    position: absolute;
    top: 224px;
    right: 224px;
    width: 53px;
    height: 35px;
    background-image: url('UIEquipEnchant.img/Main/Equip/Badge/0.png');
    background-size: 53px 35px;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    color: #000;
    z-index: 3;
}

.equip-badge .badge-text {
    margin-top: -2px;
}

/* 不同等级的徽章背景 */
.equip-badge.level-0 { background-image: url('UIEquipEnchant.img/Main/Equip/Badge/0.png'); }
.equip-badge.level-1 { background-image: url('UIEquipEnchant.img/Main/Equip/Badge/1.png'); }
.equip-badge.level-2 { background-image: url('UIEquipEnchant.img/Main/Equip/Badge/2.png'); }

/* Z-4: 强化特效背景 */
.starforce-effect-background {
    position: absolute;
    top: 126px;
    left: -4px;
    width: 509px;
    height: 153px;
    background-size: 509px 153px;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    z-index: 4;
    pointer-events: none;
}

.starforce-effect-background.active {
    opacity: 1;
}

/* Z-5: 通知框 */
.notice-background {
    position: absolute;
    top: 116px;
    left: 32px;
    width: 443px;
    height: 30px;
    background-image: url('UIEquipEnchant.img/Main/Equip/Notice/Background.png');
    background-size: 443px 30px;
    background-repeat: no-repeat;
    z-index: 5;
}

.notice-text {
    position: absolute;
    top: 116px;
    left: 32px;
    width: 443px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    color: #ffffff;
    text-align: center;
    z-index: 5;
    pointer-events: none;
}

/* Z-6: 信息面板背景 */
.info-panel-background {
    position: absolute;
    top: 280px;
    left: 21px;
    width: 466px;
    height: 254px;
    background-image: url('UIEquipEnchant.img/Main/Info/Starforce/Background.png');
    background-size: 466px 254px;
    background-repeat: no-repeat;
    z-index: 6;
}

.stats-panel-background {
    display: none;
}

/* Z-7: 概率信息内容 */
.probability-content {
    position: absolute;
    top: 310px;
    left: 21px;
    width: 466px;
    height: 100px;
    z-index: 7;
}

.prob-row {
    position: relative;
    height: 26px;
    margin-bottom: 0;
}

.prob-item {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
    background: transparent;
    border: none;
    font-size: 11px;
    width: 170px;
}

/* 根据背景图片的实际框框位置调整 */
.prob-item.success {
    top: 16px;
    left: 18px;
    width: 170px;
}

.prob-item.failure {
    top: 16px;
    left: 19px;
    width: 170px;
}

.prob-item.major-failure {
    top: 16px;
    left: 253px;
    width: 170px;
}

.prob-item.failure-drop {
    top: 16px;
    left: 253px;
    width: 170px;
}

.prob-label {
    font-size: 11px;
    color: #ffffff;
    white-space: nowrap;
}

.prob-value {
    font-size: 11px;
    font-weight: bold;
    color: #ffffff;
}

/* 切换开关样式 */
.starcatch-toggle, .prevent-toggle {
    position: relative;
    width: 15px;
    height: 15px;
}

.starcatch-toggle input, .prevent-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.starcatch-toggle label, .prevent-toggle label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #333;
    border: 1px solid #666;
    border-radius: 2px;
    transition: 0.3s;
}

.starcatch-toggle label:before, .prevent-toggle label:before {
    position: absolute;
    content: "";
    height: 8px;
    width: 8px;
    left: 2px;
    bottom: 2px;
    background: #fff;
    border-radius: 1px;
    transition: 0.3s;
    display: none;
}

.starcatch-toggle input:checked + label, .prevent-toggle input:checked + label {
    background: #4CAF50;
    border-color: #4CAF50;
}

.starcatch-toggle input:checked + label:before, .prevent-toggle input:checked + label:before {
    display: block;
}

/* 独立的Star Catching开关位置 */
.starcatch-toggle.layer-z-7 {
    position: absolute;
    top: 380px;
    left: 210px;
    width: 15px;
    height: 15px;
    z-index: 7;
}

/* 独立的防止Major Failure开关位置 */
.prevent-toggle.layer-z-7 {
    position: absolute;
    top: 380px;
    left: 445px;
    width: 15px;
    height: 15px;
    z-index: 7;
}

/* Z-7: 属性变化信息 */
.stats-content {
    position: absolute;
    top: 435px;
    left: 50px;
    width: 406px;
    height: 100px;
    padding: 5px 15px;
    z-index: 7;
}

.stats-grid {
    display: grid;
    grid-template-columns: auto 1fr auto 1fr;
    grid-template-rows: repeat(3, auto);
    gap: 4px 32px;
    align-items: center;
}

.stat-item {
    display: contents;
}

.stat-label {
    color: #ffffff;
    font-size: 11px;
    white-space: nowrap;
    text-align: left;
}

.stat-values {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 18px;
    padding: 3px 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 60px;
    text-align: center;
    margin: 0;
    font-size: 10px;
}

/* 手动指定grid位置以确保3行4列布局 */
.stat-item:nth-child(1) .stat-label { grid-column: 1; grid-row: 1; }
.stat-item:nth-child(1) .stat-values { grid-column: 2; grid-row: 1; }
.stat-item:nth-child(2) .stat-label { grid-column: 3; grid-row: 1; }
.stat-item:nth-child(2) .stat-values { grid-column: 4; grid-row: 1; }

.stat-item:nth-child(3) .stat-label { grid-column: 1; grid-row: 2; }
.stat-item:nth-child(3) .stat-values { grid-column: 2; grid-row: 2; }
.stat-item:nth-child(4) .stat-label { grid-column: 3; grid-row: 2; }
.stat-item:nth-child(4) .stat-values { grid-column: 4; grid-row: 2; }

.stat-item:nth-child(5) .stat-label { grid-column: 1; grid-row: 3; }
.stat-item:nth-child(5) .stat-values { grid-column: 2; grid-row: 3; }
.stat-item:nth-child(6) .stat-label { grid-column: 3; grid-row: 3; }
.stat-item:nth-child(6) .stat-values { grid-column: 4; grid-row: 3; }

/* Z-8: 装备物品 */
.equip-item {
    position: absolute;
    top: 167px;
    left: 206px;
    width: 70px;
    height: 70px;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    display: grid;
    place-items: center;
    font-size: 24px;
    color: #ffffff;
    transition: all 0.3s ease;
    z-index: 8;
    cursor: pointer;
}

.equip-item:hover {
    border-color: rgba(255, 255, 255, 0.6);
}

.equip-item.has-equip {
    border: none;
    background-color: transparent;
    font-size: 0;
    display: grid;
    place-items: center;
}

/* 装备图片元素样式 */
.equip-item img {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 4px;
}

.equip-item[data-item-id]:before {
    content: '';
}

/* Z-8: 星级进度显示 */
.star-progress {
    position: absolute;
    top: 275px;
    left: 154px;
    width: 200px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 8;
}

.star-level-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    font-weight: bold;
    background: transparent;
    padding: 8px 8px;
    border-radius: 0;
    border: none;
    width: 100%;
}

.star-current, .star-next {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    color: #ffd700;
    width: 40%;
}

.star-arrow {
    color: #888;
    font-size: 16px;
    display: none; /* 隐藏箭头，因为背景图片中已经有了 */
}

/* Z-10: 费用背景 - 移除背景图片，只显示文字 */
.cost-background {
    position: absolute;
    top: 532px;
    left: 40px;
    width: 457px;
    height: 80px;
    /* 移除背景图片 */
    /* background-image: url('UIEquipEnchant.img/Main/Cost/Background.png'); */
    /* background-size: 457px 80px; */
    /* background-repeat: no-repeat; */
    z-index: 10;
}

.cost-content {
    position: absolute;
    top: 532px;
    left: 40px;
    width: 457px;
    height: 80px;
    padding: 10px 15px;
    z-index: 10;
}

.cost-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    margin-bottom: 5px;
    /* 移除行背景 */
    /* background: rgba(0, 0, 0, 0.8); */
    /* border: 1px solid #444; */
    border-radius: 4px;
}

.cost-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.cost-icon {
    width: 16px;
    height: 16px;
    border-radius: 2px;
}

.meso-icon {
    background: linear-gradient(135deg, #ffd700, #ffb700);
    border: 1px solid #cc8800;
}

.star-icon {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    border: 1px solid #2c5aa0;
}

.cost-label {
    font-size: 11px;
    color: #ffffff;
}

.cost-value {
    font-size: 12px;
    font-weight: bold;
    color: #ffd700;
}

.cost-action {
    display: flex;
    gap: 5px;
}

.cost-btn {
    padding: 4px 12px;
    font-size: 10px;
    background: linear-gradient(135deg, #4a5568, #2d3748);
    color: #ffffff;
    border: 1px solid #718096;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cost-btn:hover {
    background: linear-gradient(135deg, #5a6578, #3d4758);
    transform: translateY(-1px);
}

/* Z-10: 主要按钮 - 根据XML origin调整位置 */
.enhance-button {
    position: absolute;
    bottom: 25px;
    left: 114px;
    width: 136px;
    height: 38px;
    background-image: url('UIEquipEnchant.img/Main/button_Enhance/normal/0.png');
    background-size: 136px 38px;
    background-repeat: no-repeat;
    border: none;
    cursor: pointer;
    z-index: 10;
    transition: all 0.2s ease;
}

.enhance-button:hover {
    background-image: url('UIEquipEnchant.img/Main/button_Enhance/mouseOver/0.png');
    transform: translateY(-1px);
}

.enhance-button:active {
    background-image: url('UIEquipEnchant.img/Main/button_Enhance/pressed/0.png');
    transform: translateY(0);
}

.enhance-button:disabled,
.enhance-button.disabled {
    background-image: url('UIEquipEnchant.img/Main/button_Enhance/disabled/0.png');
    cursor: not-allowed;
    transform: none;
}

.cancel-button {
    position: absolute;
    bottom: 25px;
    left: 258px;
    width: 136px;
    height: 38px;
    background-image: url('UIEquipEnchant.img/Main/button_Cancel/normal/0.png');
    background-size: 136px 38px;
    background-repeat: no-repeat;
    border: none;
    cursor: pointer;
    z-index: 10;
    transition: all 0.2s ease;
}

.cancel-button:hover {
    background-image: url('UIEquipEnchant.img/Main/button_Cancel/mouseOver/0.png');
    transform: translateY(-1px);
}

.cancel-button:active {
    background-image: url('UIEquipEnchant.img/Main/button_Cancel/pressed/0.png');
    transform: translateY(0);
}

/* Z-50: 迷你游戏覆盖层 */
.minigame-background {
    position: absolute;
    top: 200px;
    left: 50px;
    width: 200px;
    height: 80px;
    background-image: url('UIEquipEnchant.img/Common/miniGame/backgrnd.png');
    background-size: 200px 80px;
    background-repeat: no-repeat;
    z-index: 50;
}

.minigame-content {
    position: absolute;
    top: 215px;
    left: 65px;
    width: 170px;
    height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 50;
}

.minigame-gauge {
    position: relative;
    width: 150px;
    height: 20px;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #666;
    border-radius: 10px;
}

.gauge-fill {
    position: absolute;
    top: 1px;
    left: 1px;
    width: 0%;
    height: calc(100% - 2px);
    background: linear-gradient(90deg, #00ff00, #ffff00, #ff0000);
    border-radius: 8px;
    transition: width 0.1s linear;
}

.moving-star {
    position: absolute;
    top: -8px;
    left: 0%;
    font-size: 16px;
    color: #ffff00;
    animation: starMove 2s linear infinite;
    z-index: 51;
}

@keyframes starMove {
    0% { left: 0%; }
    50% { left: calc(100% - 16px); }
    100% { left: 0%; }
}

.stop-btn {
    width: 80px;
    height: 25px;
    background-image: url('UIEquipEnchant.img/Common/miniGame/button_stop/normal/0.png');
    background-size: 80px 25px;
    background-repeat: no-repeat;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.stop-btn:hover {
    background-image: url('UIEquipEnchant.img/Common/miniGame/button_stop/mouseOver/0.png');
    transform: translateY(-1px);
}

.stop-btn:active {
    background-image: url('UIEquipEnchant.img/Common/miniGame/button_stop/pressed/0.png');
    transform: translateY(0);
}

/* Z-1000: 弹出对话框 */
.popup-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
}

.popup-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 299px;
    height: 171px;
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/backgrnd.png');
    background-size: 299px 171px;
    background-repeat: no-repeat;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
}

.popup-message {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

.popup-buttons {
    display: flex;
    gap: 10px;
}

.popup-btn {
    width: 83px;
    height: 26px;
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_confirm/normal/0.png');
    background-size: 83px 26px;
    background-repeat: no-repeat;
    border: none;
    cursor: pointer;
    font-size: 11px;
    font-weight: bold;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-btn.confirm {
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_confirm/normal/0.png');
    background-size: 83px 26px;
    background-repeat: no-repeat;
    border: none;
    width: 83px;
    height: 26px;
}

.popup-btn.confirm:hover {
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_confirm/mouseOver/0.png');
    transform: translateY(-1px);
}

.popup-btn.confirm:active {
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_confirm/pressed/0.png');
    transform: translateY(0);
}

.popup-btn.cancel {
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_cancel/normal/0.png');
    background-size: 83px 26px;
    background-repeat: no-repeat;
    border: none;
    width: 83px;
    height: 26px;
}

.popup-btn.cancel:hover {
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_cancel/mouseOver/0.png');
    transform: translateY(-1px);
}

.popup-btn.cancel:active {
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_cancel/pressed/0.png');
    transform: translateY(0);
}

/* Z-1000: 结果对话框 */
.result-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
}

.result-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 299px;
    height: 171px;
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/backgrnd.png');
    background-size: 299px 171px;
    background-repeat: no-repeat;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
}

.result-message {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

.result-btn {
    width: 83px;
    height: 26px;
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_confirm/normal/0.png');
    background-size: 83px 26px;
    background-repeat: no-repeat;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.result-btn:hover {
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_confirm/mouseOver/0.png');
    transform: translateY(-1px);
}

.result-btn:active {
    background-image: url('UIEquipEnchant.img/Common/dialog/popUp/button_confirm/pressed/0.png');
    transform: translateY(0);
}

/* 强化特效动画帧 - 根据XML delay="90"ms设置 */
.effect-standby-0 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/0.png'); }
.effect-standby-1 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/1.png'); }
.effect-standby-2 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/2.png'); }
.effect-standby-3 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/3.png'); }
.effect-standby-4 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/4.png'); }
.effect-standby-5 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/5.png'); }
.effect-standby-6 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/6.png'); }
.effect-standby-7 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/7.png'); }
.effect-standby-8 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/8.png'); }
.effect-standby-9 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/9.png'); }
.effect-standby-10 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/10.png'); }
.effect-standby-11 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/11.png'); }
.effect-standby-12 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/12.png'); }
.effect-standby-13 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/13.png'); }
.effect-standby-14 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/14.png'); }
.effect-standby-15 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/15.png'); }

.effect-progress-0 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/0.png'); }
.effect-progress-1 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/1.png'); }
.effect-progress-2 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/2.png'); }
.effect-progress-3 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/3.png'); }
.effect-progress-4 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/4.png'); }
.effect-progress-5 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/5.png'); }
.effect-progress-6 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/6.png'); }
.effect-progress-7 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/7.png'); }
.effect-progress-8 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/8.png'); }
.effect-progress-9 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/9.png'); }
.effect-progress-10 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/10.png'); }
.effect-progress-11 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/11.png'); }
.effect-progress-12 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/12.png'); }
.effect-progress-13 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/13.png'); }
.effect-progress-14 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/14.png'); }
.effect-progress-15 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/15.png'); }

.effect-success-0 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/0.png'); }
.effect-success-1 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/1.png'); }
.effect-success-2 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/2.png'); }
.effect-success-3 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/3.png'); }
.effect-success-4 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/4.png'); }
.effect-success-5 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/5.png'); }
.effect-success-6 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/6.png'); }
.effect-success-7 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/7.png'); }
.effect-success-8 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/8.png'); }
.effect-success-9 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/9.png'); }
.effect-success-10 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/10.png'); }
.effect-success-11 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/11.png'); }
.effect-success-12 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/12.png'); }
.effect-success-13 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/13.png'); }
.effect-success-14 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/14.png'); }
.effect-success-15 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/15.png'); }
.effect-success-16 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/16.png'); }
.effect-success-17 { background-image: url('UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/17.png'); }

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: scale(1); }
    to { opacity: 0; transform: scale(0.9); }
}

/* 强化成功/失败特效 */
.enhance-success {
    animation: successPulse 0.6s ease-in-out;
}

.enhance-fail {
    animation: failShake 0.6s ease-in-out;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.05); filter: brightness(1.3); }
}

@keyframes failShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

/* 响应式设计 */
@media (max-width: 600px) {
    .ui-container {
        transform: scale(0.8);
        transform-origin: center;
    }
}

@media (max-width: 480px) {
    .ui-container {
        transform: scale(0.6);
        transform-origin: center;
    }
}

/* 分层控制面板 */
.layer-control-panel {
    position: fixed;
    top: 10px;
    left: 10px;
    width: 300px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #444;
    border-radius: 8px;
    padding: 15px;
    z-index: 10000;
    color: #fff;
    font-size: 12px;
}

.layer-control-panel h3 {
    margin: 0 0 10px 0;
    color: #ffd700;
    text-align: center;
}

.layer-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5px;
    margin-bottom: 10px;
}

.layer-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 2px;
    cursor: pointer;
    transition: background 0.2s;
}

.layer-controls label:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.layer-controls input[type="checkbox"] {
    width: 12px;
    height: 12px;
}

.layer-control-panel button {
    width: 48%;
    padding: 5px;
    margin: 2px 1%;
    background: linear-gradient(135deg, #4a5568, #2d3748);
    color: #fff;
    border: 1px solid #666;
    border-radius: 4px;
    cursor: pointer;
    font-size: 10px;
}

.layer-control-panel button:hover {
    background: linear-gradient(135deg, #5a6578, #3d4758);
}

/* 分层类定义 */
.layer-z-0 { z-index: 0; }
.layer-z-1 { z-index: 1; }
.layer-z-2 { z-index: 2; }
.layer-z-3 { z-index: 3; }
.layer-z-4 { z-index: 4; }
.layer-z-5 { z-index: 5; }
.layer-z-6 { z-index: 6; }
.layer-z-7 { z-index: 7; }
.layer-z-8 { z-index: 8; }
.layer-z-10 { z-index: 10; }
.layer-z-50 { z-index: 50; }
.layer-z-1000 { z-index: 1000; }

/* 隐藏层的样式 */
.layer-hidden {
    display: none !important;
}

/* 移除所有特殊位置调整 */

.stat-current {
    color: #ffffff;
    text-align: center;
    flex-shrink: 0;
}

.stat-arrow {
    color: #cccccc;
    font-size: 10px;
    display: inline;
    flex-shrink: 0;
}

.stat-next {
    color: #00ff00;
    font-weight: bold;
    text-align: center;
    flex-shrink: 0;
}