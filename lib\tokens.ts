import crypto from 'crypto'
import { SignJWT, jwtVerify } from 'jose'

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'fallback-secret-key')

/**
 * 生成随机验证令牌
 */
export function generateVerificationToken(): string {
  return crypto.randomBytes(32).toString('hex')
}

/**
 * 生成安全的JWT令牌
 */
export async function generateSecureToken(
  payload: Record<string, any>,
  expiresIn: string = '15m'
): Promise<string> {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(expiresIn)
    .setJti(crypto.randomUUID()) // 唯一标识符
    .sign(JWT_SECRET)
}

/**
 * 验证JWT令牌
 */
export async function verifySecureToken(token: string): Promise<any> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    return payload
  } catch (error) {
    throw new Error('Invalid token')
  }
}

/**
 * 生成密码重置令牌
 */
export function generatePasswordResetToken(): string {
  return crypto.randomBytes(32).toString('hex')
}

/**
 * 生成API密钥
 */
export function generateApiKey(): string {
  const prefix = 'mxd_'
  const randomPart = crypto.randomBytes(24).toString('base64url')
  return prefix + randomPart
}

/**
 * 生成会话ID
 */
export function generateSessionId(): string {
  return crypto.randomBytes(16).toString('hex')
}

/**
 * 创建CSRF令牌
 */
export function generateCSRFToken(sessionId: string): string {
  const timestamp = Date.now().toString()
  const data = `${sessionId}:${timestamp}`
  const hash = crypto.createHmac('sha256', JWT_SECRET).update(data).digest('hex')
  return `${timestamp}:${hash}`
}

/**
 * 验证CSRF令牌
 */
export function verifyCSRFToken(token: string, sessionId: string): boolean {
  try {
    const [timestamp, hash] = token.split(':')
    const data = `${sessionId}:${timestamp}`
    const expectedHash = crypto.createHmac('sha256', JWT_SECRET).update(data).digest('hex')
    
    // 验证哈希值
    if (!crypto.timingSafeEqual(Buffer.from(hash, 'hex'), Buffer.from(expectedHash, 'hex'))) {
      return false
    }
    
    // 验证时间戳（1小时有效期）
    const tokenTime = parseInt(timestamp)
    const now = Date.now()
    const oneHour = 60 * 60 * 1000
    
    return (now - tokenTime) < oneHour
  } catch (error) {
    return false
  }
}

/**
 * 生成设备指纹令牌
 */
export function generateDeviceToken(fingerprint: string, userAgent: string): string {
  const data = `${fingerprint}:${userAgent}:${Date.now()}`
  return crypto.createHash('sha256').update(data).digest('hex')
}

/**
 * 生成一次性令牌（OTP）
 */
export function generateOTP(length: number = 6): string {
  const digits = '0123456789'
  let otp = ''
  
  for (let i = 0; i < length; i++) {
    otp += digits[crypto.randomInt(0, digits.length)]
  }
  
  return otp
}

/**
 * 加密敏感数据
 */
export function encryptData(data: string): string {
  const algorithm = 'aes-256-gcm'
  const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'fallback-key', 'salt', 32)
  const iv = crypto.randomBytes(16)
  
  const cipher = crypto.createCipher(algorithm, key)
  cipher.setAAD(Buffer.from('additional-data'))
  
  let encrypted = cipher.update(data, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  
  const authTag = cipher.getAuthTag()
  
  return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`
}

/**
 * 解密敏感数据
 */
export function decryptData(encryptedData: string): string {
  const algorithm = 'aes-256-gcm'
  const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'fallback-key', 'salt', 32)
  
  const [ivHex, authTagHex, encrypted] = encryptedData.split(':')
  const iv = Buffer.from(ivHex, 'hex')
  const authTag = Buffer.from(authTagHex, 'hex')
  
  const decipher = crypto.createDecipher(algorithm, key)
  decipher.setAAD(Buffer.from('additional-data'))
  decipher.setAuthTag(authTag)
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  
  return decrypted
}
