import { createClient } from 'redis'

const globalForRedis = globalThis as unknown as {
  redis: ReturnType<typeof createClient> | undefined
}

export const redis = globalForRedis.redis ?? createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
})

if (process.env.NODE_ENV !== 'production') {
  globalForRedis.redis = redis
}

// 连接 Redis
if (!redis.isOpen) {
  redis.connect().catch(console.error)
}

// 错误处理
redis.on('error', (err) => {
  console.error('Redis Client Error:', err)
})

redis.on('connect', () => {
  console.log('Redis Client Connected')
})

redis.on('ready', () => {
  console.log('Redis Client Ready')
})
