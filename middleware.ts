import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // 公开路径，无需认证
    const publicPaths = [
      '/',
      '/login',
      '/register',
      '/reset-password',
      '/verify-email',
      '/tools',
      '/cms-216',
      '/api/auth',
      '/api/public'
    ]

    // 检查是否为公开路径
    const isPublicPath = publicPaths.some(path => 
      pathname === path || pathname.startsWith(path + '/')
    )

    // 如果是公开路径，直接通过
    if (isPublicPath) {
      return NextResponse.next()
    }

    // 需要认证的路径
    const protectedPaths = [
      '/dashboard',
      '/profile',
      '/transactions',
      '/settings',
      '/admin'
    ]

    const isProtectedPath = protectedPaths.some(path => 
      pathname.startsWith(path)
    )

    // 如果访问受保护路径但未登录，重定向到登录页
    if (isProtectedPath && !token) {
      const loginUrl = new URL('/login', req.url)
      loginUrl.searchParams.set('callbackUrl', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // 管理员路径权限检查
    if (pathname.startsWith('/admin')) {
      const userRoles = token?.roles as string[] || []
      const isAdmin = userRoles.includes('admin') || userRoles.includes('super_admin')
      
      if (!isAdmin) {
        return NextResponse.redirect(new URL('/dashboard', req.url))
      }
    }

    // API 路由保护
    if (pathname.startsWith('/api/protected/')) {
      if (!token) {
        return new NextResponse(
          JSON.stringify({ error: 'Unauthorized' }),
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        )
      }
    }

    // 速率限制检查（简化版）
    if (pathname.startsWith('/api/')) {
      const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown'
      // 这里可以集成 Redis 进行更复杂的速率限制
      // 暂时跳过实现
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // 公开路径始终允许访问
        const publicPaths = [
          '/',
          '/login',
          '/register',
          '/reset-password',
          '/verify-email',
          '/tools',
          '/cms-216'
        ]

        const isPublicPath = publicPaths.some(path => 
          pathname === path || pathname.startsWith(path + '/')
        )

        if (isPublicPath) {
          return true
        }

        // 其他路径需要认证
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public|images).*)',
  ],
}
