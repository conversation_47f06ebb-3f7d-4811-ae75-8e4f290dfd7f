import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { getToken } from "next-auth/jwt"

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 公开路径，无需认证
  const publicPaths = [
    '/',
    '/login',
    '/register',
    '/reset-password',
    '/verify-email',
    '/tools',
    '/cms-216',
    '/api/auth',
    '/api/public'
  ]

  // 检查是否为公开路径
  const isPublicPath = publicPaths.some(path =>
    pathname === path || pathname.startsWith(path + '/')
  )

  // 如果是公开路径，直接通过
  if (isPublicPath) {
    return NextResponse.next()
  }

  // 获取用户 token
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  })

  // 需要认证的路径
  const protectedPaths = [
    '/dashboard',
    '/profile',
    '/transactions',
    '/settings',
    '/currency',
    '/recharge',
    '/admin'
  ]

  const isProtectedPath = protectedPaths.some(path =>
    pathname.startsWith(path)
  )

  // 如果访问受保护路径但未登录，重定向到登录页
  if (isProtectedPath && !token) {
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // 管理员路径权限检查
  if (pathname.startsWith('/admin')) {
    const userRoles = token?.roles as string[] || []
    const isAdmin = userRoles.includes('admin') || userRoles.includes('super_admin')

    if (!isAdmin) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
  }

  // API 路由保护
  if (pathname.startsWith('/api/protected/')) {
    if (!token) {
      return new NextResponse(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }
  }

  // 速率限制检查（简化版）
  if (pathname.startsWith('/api/')) {
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    // 这里可以集成 Redis 进行更复杂的速率限制
    // 暂时跳过实现
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public|images).*)',
  ],
}
