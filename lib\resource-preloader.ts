// 资源预加载工具

// 全局类型声明
declare global {
  interface Window {
    itemListData?: any[]
  }
}

export interface PreloadProgress {
  loaded: number
  total: number
  percentage: number
  currentResource: string
}

export type ProgressCallback = (progress: PreloadProgress) => void

export class ResourcePreloader {
  private loadedCount = 0
  private totalCount = 0
  private progressCallback?: ProgressCallback

  constructor(progressCallback?: ProgressCallback) {
    this.progressCallback = progressCallback
  }

  // 预加载图片批次
  async preloadImages(imageUrls: string[]): Promise<void> {
    this.totalCount = imageUrls.length
    this.loadedCount = 0

    if (this.totalCount === 0) {
      return Promise.resolve()
    }

    return new Promise((resolve) => {
      let completed = 0

      const checkComplete = (src: string) => {
        this.loadedCount++
        completed++
        
        this.updateProgress(src)
        
        if (completed >= this.totalCount) {
          resolve()
        }
      }

      imageUrls.forEach(src => {
        const img = new Image()
        
        img.onload = () => {
          console.log(`✓ 图片加载成功: ${src}`)
          checkComplete(src)
        }
        
        img.onerror = () => {
          console.warn(`✗ 图片加载失败: ${src}`)
          checkComplete(src) // 即使失败也继续
        }
        
        img.src = src
      })
    })
  }

  // 预加载装备数据库
  async preloadItemDatabase(): Promise<any> {
    try {
      console.log('开始加载装备数据库...')

      // 首先尝试加载新的 itemList.js 文件
      try {
        const script = document.createElement('script')
        script.src = '/data/itemList.js'

        return new Promise((resolve, reject) => {
          script.onload = () => {
            // 检查全局变量是否存在
            if (window.itemListData && Array.isArray(window.itemListData)) {
              console.log(`从 itemList.js 加载了 ${window.itemListData.length} 个装备`)
              resolve(window.itemListData)
            } else {
              reject(new Error('itemList.js 加载失败或数据格式错误'))
            }
          }

          script.onerror = () => {
            reject(new Error('无法加载 itemList.js'))
          }

          document.head.appendChild(script)
        })
      } catch (jsError) {
        console.warn('加载 itemList.js 失败，尝试加载 JSON 文件:', jsError)

        // 回退到 JSON 文件
        const response = await fetch('/data/itemList.json')
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        return await response.json()
      }
    } catch (error) {
      console.error('加载装备数据库失败:', error)
      // 返回测试数据作为备用
      return this.createTestItemData()
    }
  }

  // 创建测试装备数据
  private createTestItemData() {
    return [
      {
        itemId: 1000009,
        itemName: "Red M-Forcer Helmet",
        itemCategory: 1000301001,
        imageUrl: "https://api-static.msu.io/itemimages/icon/1000009.png",
        detailInfo: {
          metadata: {
            category: {
              categoryNo: 1000301001,
              label: "Item > Decoration > Outfits > Hat",
              tier3: { label: "Hat", code: "001" }
            },
            common: {
              itemName: "Red M-Forcer Helmet",
              itemId: 1000009,
              enableStarforce: true,
              blockUpgradePotential: false,
              blockUpgradeExtraOption: false,
              isCashItem: true
            },
            required: { level: 0 },
            stats: {
              str: 0, dex: 0, int: 0, luk: 0,
              maxHp: 0, pdd: 0
            }
          }
        }
      },
      {
        itemId: 1001164,
        itemName: "黑色贝雷帽",
        itemCategory: 1000301001,
        imageUrl: "https://api-static.msu.io/itemimages/icon/1001164.png",
        detailInfo: {
          metadata: {
            category: {
              categoryNo: 1000301001,
              label: "Item > Decoration > Outfits > Hat",
              tier3: { label: "Hat", code: "001" }
            },
            common: {
              itemName: "黑色贝雷帽",
              itemId: 1001164,
              enableStarforce: true,
              blockUpgradePotential: false,
              blockUpgradeExtraOption: false,
              isCashItem: false
            },
            required: { level: 10 },
            stats: {
              str: 2, dex: 2, int: 2, luk: 2,
              maxHp: 15, pdd: 5
            }
          }
        }
      },
      {
        itemId: 1072952,
        itemName: "黑色战斗靴",
        itemCategory: 1000301005,
        imageUrl: "https://api-static.msu.io/itemimages/icon/1072952.png",
        detailInfo: {
          metadata: {
            category: {
              categoryNo: 1000301005,
              label: "Item > Equipment > Shoes",
              tier3: { label: "Shoes", code: "005" }
            },
            common: {
              itemName: "黑色战斗靴",
              itemId: 1072952,
              enableStarforce: true,
              blockUpgradePotential: false,
              blockUpgradeExtraOption: false,
              isCashItem: false
            },
            required: { level: 30 },
            stats: {
              str: 5, dex: 5, int: 0, luk: 0,
              maxHp: 25, pdd: 8
            }
          }
        }
      },
      {
        itemId: 1702424,
        itemName: "传说之剑",
        itemCategory: 1000301007,
        imageUrl: "https://api-static.msu.io/itemimages/icon/1702424.png",
        detailInfo: {
          metadata: {
            category: {
              categoryNo: 1000301007,
              label: "Item > Equipment > Weapon",
              tier3: { label: "Weapon", code: "007" }
            },
            common: {
              itemName: "传说之剑",
              itemId: 1702424,
              enableStarforce: true,
              blockUpgradePotential: false,
              blockUpgradeExtraOption: false,
              isCashItem: false
            },
            required: { level: 100 },
            stats: {
              str: 50, dex: 0, int: 0, luk: 0,
              maxHp: 100, pdd: 20
            }
          }
        }
      }
    ]
  }

  // 获取UI资源列表
  getUIImageList(): string[] {
    return [
      // 主要UI背景
      '/images/UIEquipEnchant/Main/Background.png',
      
      // 装备槽
      '/images/UIEquipEnchant/Main/Equip/Slot/Starforce.png',
      '/images/UIEquipEnchant/Main/Equip/Slot/Potential.png',
      '/images/UIEquipEnchant/Main/Equip/Slot/Bonusstat.png',
      
      // 徽章
      '/images/UIEquipEnchant/Main/Equip/Badge/0.png',
      '/images/UIEquipEnchant/Main/Equip/Badge/1.png',
      '/images/UIEquipEnchant/Main/Equip/Badge/2.png',
      
      // 通知框
      '/images/UIEquipEnchant/Main/Equip/Notice/Background.png',
      
      // 信息面板
      '/images/UIEquipEnchant/Main/Info/Starforce/Background.png',
      
      // Tab分割线
      '/images/UIEquipEnchant/Main/Tab/Image_Line.png',
      
      // 标签页按钮 - 星力强化
      '/images/UIEquipEnchant/Main/Tab/Starforce/normal/0.png',
      '/images/UIEquipEnchant/Main/Tab/Starforce/selected/0.png',
      '/images/UIEquipEnchant/Main/Tab/Starforce/disabled/0.png',
      
      // 标签页按钮 - 潜能
      '/images/UIEquipEnchant/Main/Tab/Potential/normal/0.png',
      '/images/UIEquipEnchant/Main/Tab/Potential/selected/0.png',
      '/images/UIEquipEnchant/Main/Tab/Potential/disabled/0.png',
      
      // 标签页按钮 - 额外属性
      '/images/UIEquipEnchant/Main/Tab/Bonusstat/normal/0.png',
      '/images/UIEquipEnchant/Main/Tab/Bonusstat/selected/0.png',
      '/images/UIEquipEnchant/Main/Tab/Bonusstat/disabled/0.png',
      
      // 主要按钮
      '/images/UIEquipEnchant/Main/button_Enhance/normal/0.png',
      '/images/UIEquipEnchant/Main/button_Enhance/mouseOver/0.png',
      '/images/UIEquipEnchant/Main/button_Enhance/pressed/0.png',
      '/images/UIEquipEnchant/Main/button_Enhance/disabled/0.png',
      
      '/images/UIEquipEnchant/Main/button_Cancel/normal/0.png',
      '/images/UIEquipEnchant/Main/button_Cancel/mouseOver/0.png',
      '/images/UIEquipEnchant/Main/button_Cancel/pressed/0.png',
    ]
  }

  // 获取特效动画资源列表
  getEffectImageList(): string[] {
    const effects: string[] = []
    
    // 待机特效
    for (let i = 0; i < 16; i++) {
      effects.push(`/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Standby/${i}.png`)
    }
    
    // 进行中特效
    for (let i = 0; i < 16; i++) {
      effects.push(`/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Progress/Loop/${i}.png`)
    }
    
    // 成功特效
    for (let i = 0; i < 18; i++) {
      effects.push(`/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Result/Success/${i}.png`)
    }
    
    return effects
  }

  private updateProgress(currentResource: string) {
    if (this.progressCallback) {
      this.progressCallback({
        loaded: this.loadedCount,
        total: this.totalCount,
        percentage: Math.round((this.loadedCount / this.totalCount) * 100),
        currentResource
      })
    }
  }
}
