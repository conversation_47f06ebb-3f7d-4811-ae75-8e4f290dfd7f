'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Crown, Star, Shield, Sparkles } from 'lucide-react'
import { SessionUser } from '@/types/auth'

interface MembershipCardProps {
  user: any
  membershipLevel: SessionUser['membershipLevel']
}

export function MembershipCard({ user, membershipLevel }: MembershipCardProps) {
  const getMembershipInfo = (level: string) => {
    switch (level) {
      case 'diamond':
        return {
          title: '钻石用户',
          icon: Crown,
          color: 'text-purple-600',
          bgColor: 'bg-gradient-to-r from-purple-500 to-pink-500',
          textColor: 'text-white',
          description: '享受最高级别的服务和功能',
          benefits: ['无限制使用所有功能', '优先客服支持', '专属活动参与', '数据导出功能']
        }
      case 'vip':
        return {
          title: '黄金用户',
          icon: Star,
          color: 'text-yellow-600',
          bgColor: 'bg-gradient-to-r from-yellow-400 to-orange-500',
          textColor: 'text-white',
          description: '享受高级功能和优质服务',
          benefits: ['高级功能使用', '优先支持', '数据导出', '个性化设置']
        }
      case 'registered':
        return {
          title: '注册用户',
          icon: Shield,
          color: 'text-blue-600',
          bgColor: 'bg-gradient-to-r from-blue-500 to-cyan-500',
          textColor: 'text-white',
          description: '享受基础功能和服务',
          benefits: ['基础功能使用', '数据保存', '个人设置', '社区参与']
        }
      case 'admin':
        return {
          title: '管理员',
          icon: Sparkles,
          color: 'text-red-600',
          bgColor: 'bg-gradient-to-r from-red-500 to-pink-500',
          textColor: 'text-white',
          description: '系统管理员权限',
          benefits: ['所有功能权限', '用户管理', '系统配置', '数据分析']
        }
      default:
        return {
          title: '游客用户',
          icon: Shield,
          color: 'text-gray-600',
          bgColor: 'bg-gradient-to-r from-gray-400 to-gray-500',
          textColor: 'text-white',
          description: '基础访问权限',
          benefits: ['浏览公开内容', '使用基础工具']
        }
    }
  }

  const membershipInfo = getMembershipInfo(membershipLevel)
  const Icon = membershipInfo.icon

  return (
    <Card className="overflow-hidden">
      <div className={`${membershipInfo.bgColor} p-6`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white/20 rounded-lg">
              <Icon className={`h-6 w-6 ${membershipInfo.textColor}`} />
            </div>
            <div>
              <h3 className={`text-xl font-bold ${membershipInfo.textColor}`}>
                {membershipInfo.title}
              </h3>
              <p className={`text-sm ${membershipInfo.textColor} opacity-90`}>
                {membershipInfo.description}
              </p>
            </div>
          </div>
          <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
            {membershipLevel === 'admin' ? '管理员' : '活跃用户'}
          </Badge>
        </div>
      </div>
      
      <CardContent className="p-6">
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">会员权益</h4>
            <ul className="space-y-2">
              {membershipInfo.benefits.map((benefit, index) => (
                <li key={index} className="flex items-center text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2" />
                  {benefit}
                </li>
              ))}
            </ul>
          </div>
          
          {membershipLevel === 'registered' && (
            <div className="pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-600 mb-3">
                升级到黄金用户，解锁更多功能
              </p>
              <Button size="sm" className="w-full">
                升级会员
              </Button>
            </div>
          )}
          
          {user?.currencyBalance && (
            <div className="pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">欢乐豆余额</span>
                <span className="font-semibold text-gray-900">
                  {Number(user.currencyBalance.balance).toLocaleString()}
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
