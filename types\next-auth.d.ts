import { DefaultSession, DefaultUser } from "next-auth"
import { JWT, DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      avatar?: string | null
      roles: string[]
      permissions: string[]
      membershipLevel: 'guest' | 'registered' | 'vip' | 'diamond' | 'admin'
    } & DefaultSession["user"]
  }

  interface User {
    id: string
    email: string
    name?: string | null
    avatar?: string | null
    roles?: string[]
    permissions?: string[]
    membershipLevel?: 'guest' | 'registered' | 'vip' | 'diamond' | 'admin'
    emailVerified?: Date | null
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    userId: string
    email: string
    name?: string | null
    avatar?: string | null
    roles: string[]
    permissions: string[]
    membershipLevel: 'guest' | 'registered' | 'vip' | 'diamond' | 'admin'
  }
}
