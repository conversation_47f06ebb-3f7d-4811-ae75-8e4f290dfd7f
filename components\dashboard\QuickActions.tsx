'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Zap, 
  CreditCard, 
  Settings, 
  Download, 
  Star, 
  Wrench,
  BarChart3,
  Gift
} from 'lucide-react'
import Link from 'next/link'
import { SessionUser } from '@/types/auth'

interface QuickActionsProps {
  membershipLevel: SessionUser['membershipLevel']
}

export function QuickActions({ membershipLevel }: QuickActionsProps) {
  const getAvailableActions = () => {
    const baseActions = [
      {
        title: '装备强化',
        description: '使用强化模拟器',
        icon: Zap,
        href: '/tools/enhancement',
        color: 'bg-blue-50 text-blue-600',
        available: true
      },
      {
        title: '充值中心',
        description: '购买欢乐豆',
        icon: CreditCard,
        href: '/recharge',
        color: 'bg-green-50 text-green-600',
        available: true
      },
      {
        title: '个人设置',
        description: '管理账户设置',
        icon: Settings,
        href: '/settings',
        color: 'bg-gray-50 text-gray-600',
        available: true
      }
    ]

    const premiumActions = [
      {
        title: '数据导出',
        description: '导出使用数据',
        icon: Download,
        href: '/export',
        color: 'bg-purple-50 text-purple-600',
        available: membershipLevel === 'vip' || membershipLevel === 'diamond' || membershipLevel === 'admin'
      },
      {
        title: '高级工具',
        description: '使用高级功能',
        icon: Wrench,
        href: '/tools/advanced',
        color: 'bg-yellow-50 text-yellow-600',
        available: membershipLevel === 'vip' || membershipLevel === 'diamond' || membershipLevel === 'admin'
      }
    ]

    const adminActions = [
      {
        title: '数据分析',
        description: '查看系统统计',
        icon: BarChart3,
        href: '/admin/analytics',
        color: 'bg-red-50 text-red-600',
        available: membershipLevel === 'admin'
      }
    ]

    return [...baseActions, ...premiumActions, ...adminActions].filter(action => action.available)
  }

  const actions = getAvailableActions()

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Star className="h-5 w-5 text-yellow-500" />
          <span>快速操作</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {actions.map((action, index) => {
            const Icon = action.icon
            return (
              <Button
                key={index}
                variant="ghost"
                className="w-full justify-start h-auto p-4 hover:bg-gray-50"
                asChild
              >
                <Link href={action.href}>
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex flex-col items-start">
                      <span className="font-medium text-gray-900">
                        {action.title}
                      </span>
                      <span className="text-xs text-gray-500">
                        {action.description}
                      </span>
                    </div>
                  </div>
                </Link>
              </Button>
            )
          })}
          
          {/* 会员升级提示 */}
          {membershipLevel === 'registered' && (
            <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Gift className="h-4 w-4 text-yellow-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">
                    升级到黄金用户
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    解锁数据导出、高级工具等更多功能
                  </p>
                  <Button size="sm" className="bg-yellow-500 hover:bg-yellow-600">
                    立即升级
                  </Button>
                </div>
              </div>
            </div>
          )}
          
          {/* 功能提示 */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              💡 使用提示
            </h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 每日登录可获得欢乐豆奖励</li>
              <li>• 使用工具消耗欢乐豆获得功能</li>
              <li>• 邀请朋友注册可获得额外奖励</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
