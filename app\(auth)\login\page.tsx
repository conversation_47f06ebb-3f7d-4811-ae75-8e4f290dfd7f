import { Metadata } from 'next'
import { LoginForm } from '@/components/auth/LoginForm'

export const metadata: Metadata = {
  title: '用户登录 - 冒险岛情报站',
  description: '登录您的账户以访问高级功能',
}

export default function LoginPage() {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-lg shadow-lg p-8">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          欢迎回来
        </h2>
        <p className="text-gray-600">
          登录您的账户以继续使用
        </p>
      </div>
      
      <LoginForm />
      
      <div className="mt-6 text-center">
        <p className="text-sm text-gray-600">
          还没有账户？{' '}
          <a 
            href="/register" 
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            立即注册
          </a>
        </p>
        <p className="text-sm text-gray-600 mt-2">
          忘记密码？{' '}
          <a 
            href="/reset-password" 
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            重置密码
          </a>
        </p>
      </div>
    </div>
  )
}
