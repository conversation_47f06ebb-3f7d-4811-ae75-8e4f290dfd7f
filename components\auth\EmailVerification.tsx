'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, XCircle, Mail } from 'lucide-react'

interface EmailVerificationProps {
  token?: string
  email?: string
}

export function EmailVerification({ token, email }: EmailVerificationProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isVerified, setIsVerified] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  useEffect(() => {
    if (token) {
      verifyEmail(token)
    } else {
      setIsLoading(false)
      setError('验证链接无效')
    }
  }, [token])

  const verifyEmail = async (verificationToken: string) => {
    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '邮箱验证失败')
      }

      setIsVerified(true)
      setTimeout(() => {
        router.push('/login?message=email-verified')
      }, 3000)
    } catch (error) {
      setError(error instanceof Error ? error.message : '邮箱验证失败')
    } finally {
      setIsLoading(false)
    }
  }

  const resendVerification = async () => {
    if (!email) {
      setError('无法重新发送验证邮件，邮箱地址缺失')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '重新发送验证邮件失败')
      }

      setError('')
      // 可以显示成功消息
    } catch (error) {
      setError(error instanceof Error ? error.message : '重新发送验证邮件失败')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="text-center space-y-4">
        <Loader2 className="h-16 w-16 text-blue-500 mx-auto animate-spin" />
        <h3 className="text-lg font-semibold text-gray-900">正在验证邮箱...</h3>
        <p className="text-gray-600">
          请稍候，我们正在验证您的邮箱地址
        </p>
      </div>
    )
  }

  if (isVerified) {
    return (
      <div className="text-center space-y-4">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
        <h3 className="text-lg font-semibold text-gray-900">邮箱验证成功！</h3>
        <p className="text-gray-600">
          您的邮箱已成功验证，现在可以使用所有功能了。
        </p>
        <p className="text-sm text-gray-500">
          正在跳转到登录页面...
        </p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center space-y-4">
        <XCircle className="h-16 w-16 text-red-500 mx-auto" />
        <h3 className="text-lg font-semibold text-gray-900">验证失败</h3>
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        
        {email && (
          <div className="space-y-4">
            <p className="text-gray-600">
              验证链接可能已过期或无效，您可以重新发送验证邮件。
            </p>
            <Button
              onClick={resendVerification}
              disabled={isLoading}
              variant="outline"
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Mail className="mr-2 h-4 w-4" />
              重新发送验证邮件
            </Button>
          </div>
        )}
        
        <div className="pt-4">
          <Button
            onClick={() => router.push('/login')}
            variant="outline"
          >
            返回登录
          </Button>
        </div>
      </div>
    )
  }

  return null
}
