# 数据库配置
DATABASE_URL="postgresql://postgres:postgres@localhost:5433/mxd_info_db"
REDIS_URL="redis://localhost:6379"

# NextAuth配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-super-secret-key-change-this-in-production"

# 邮件服务 (Resend)
RESEND_API_KEY="re_your-resend-api-key"
EMAIL_FROM="<EMAIL>"

# 第三方服务
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY="your-fingerprint-js-api-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-key"
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret"

# 安全配置
JWT_SECRET="your-jwt-secret-at-least-32-characters-long"
ENCRYPTION_KEY="your-encryption-key-at-least-32-characters"

# 应用配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="冒险岛情报站"

# 开发配置
NODE_ENV="development"
LOG_LEVEL="debug"

# 虚拟货币配置
CURRENCY_NAME="欢乐豆"
DEFAULT_CURRENCY_AMOUNT="100"

# 功能开关
ENABLE_REGISTRATION="true"
ENABLE_EMAIL_VERIFICATION="true"
ENABLE_FINGERPRINT_TRACKING="true"
