# 冒险岛情报站 (MapleStory Information Hub)

一个专业的冒险岛游戏数据库和工具集合网站，提供装备、怪物、技能、地图等全面信息以及实用的游戏工具。

## 🎮 项目概述

冒险岛情报站是基于 Next.js 14 构建的现代化游戏信息网站，采用 App Router 架构，提供：

- 📊 **游戏数据库**：装备、道具、技能等详细信息
- 🛠️ **实用工具**：装备强化模拟器、星之力强化模拟器、纸娃娃模拟器、洗魔方模拟器等
- 🆕 **版本更新**：CMS-216 版本新增道具展示
- 📱 **响应式设计**：支持桌面端和移动端访问

## 🏗️ 项目结构分析

### 目录组织

```
maplestory-info-station/
├── app/                    # Next.js 13+ App Router 页面
│   ├── cms-216/           # CMS-216 版本新增道具页面
│   ├── cubes/             # 洗魔方模拟器
│   ├── database/          # 游戏数据库
│   ├── hyperstats/        # 超级属性计算器
│   ├── items/[id]/        # 动态道具详情页面
│   ├── paperdoll/         # 纸娃娃模拟器
│   ├── starforce/         # 星之力强化模拟器
│   ├── tools/             # 工具集合页面
│   ├── layout.tsx         # 全局布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── ui/               # shadcn/ui 组件库
│   ├── header.tsx        # 网站头部导航
│   ├── sidebar.tsx       # 侧边栏
│   ├── item-card.tsx     # 道具卡片组件
│   └── item-tooltip.tsx  # 道具提示框
├── public/               # 静态资源
│   ├── images/           # 图片资源
│   │   ├── cms-216/      # CMS-216 版本道具图片
│   │   └── ...
│   └── data/             # 数据文件
├── scripts/              # 构建和数据生成脚本
├── hooks/                # 自定义 React Hooks
└── lib/                  # 工具函数
```

### 模块化设计模式

项目采用了清晰的关注点分离：

- **页面层** (`app/`): 使用 Next.js App Router，每个功能模块独立页面
- **组件层** (`components/`): 可复用的 UI 组件，基于 shadcn/ui 设计系统
- **数据层** (`public/data/`): 静态数据文件和图片资源
- **工具层** (`lib/`, `hooks/`): 通用工具函数和自定义 Hooks

## 🛣️ 页面和路由分析

### 路由架构

项目使用 **Next.js 13+ App Router**，采用文件系统路由：

#### 静态路由
- `/` - 首页，展示网站概览和功能导航
- `/cms-216` - CMS-216 版本新增道具列表
- `/database` - 游戏数据库主页
- `/tools/enhancement` - 装备强化模拟器（新增）
- `/starforce` - 星之力强化模拟器
- `/paperdoll` - 纸娃娃模拟器
- `/cubes` - 洗魔方模拟器
- `/hyperstats` - 超级属性计算器
- `/tools` - 工具集合页面

#### 动态路由
- `/items/[id]` - 道具详情页面，支持动态 ID 参数

### 路由特性

- **静态生成 (SSG)**: 配置了 `output: 'export'` 用于静态导出
- **动态参数**: 道具详情页支持 URL 编码的道具 ID
- **预生成路径**: 使用 `generateStaticParams` 预生成常见道具页面

## 🏛️ 系统架构

### 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                        用户界面层                              │
├─────────────────────────────────────────────────────────────┤
│  首页    │  数据库  │  工具集  │  道具详情  │  模拟器        │
│ page.tsx │ database │  tools   │ items/[id] │ starforce     │
├─────────────────────────────────────────────────────────────┤
│                        组件层                                │
├─────────────────────────────────────────────────────────────┤
│  Header  │ Sidebar  │ ItemCard │ ItemTooltip │ UI Components │
├─────────────────────────────────────────────────────────────┤
│                        数据层                                │
├─────────────────────────────────────────────────────────────┤
│  静态图片  │  JSON数据  │  配置文件  │  脚本工具            │
│ /images   │  /data     │  configs   │  /scripts            │
└─────────────────────────────────────────────────────────────┘
```

### 数据流架构

```
文件系统 → 服务端读取 → 静态生成 → 客户端渲染 → 用户交互
    ↓           ↓           ↓           ↓           ↓
图片文件 → getCMS216Items → 预生成页面 → React组件 → 事件处理
```

## ⚙️ 功能实现分析

### 1. 道具展示系统

**涉及文件**:
- `app/cms-216/page.tsx` - 道具列表页面
- `app/items/[id]/page.tsx` - 道具详情页面
- `components/item-card.tsx` - 道具卡片组件

**功能特点**:
- 自动读取 `public/images/cms-216/` 目录下的图片文件
- 文件名自动转换为道具名称
- 支持多种图片格式 (PNG, JPG, JPEG, GIF, WEBP)
- 点击道具跳转到详情页面
- 图片加载失败时显示占位符

**数据流**:
```
文件系统 → getCMS216Items() → 道具列表 → ItemCard 组件 → 详情页面
```

### 2. 星之力强化模拟器

**涉及文件**:
- `app/starforce/page.tsx` - 强化模拟器主页面

**功能特点**:
- 真实的强化成功率数据
- 支持防止损毁、MVP 优惠、活动优惠等选项
- 实时模拟强化过程
- 统计尝试次数、总花费等数据
- 可视化进度显示

**核心算法**:
```typescript
// 强化模拟逻辑
while (stars < targetStars && attempts < maxAttempts) {
  const random = Math.random() * 100
  if (random <= successRate) {
    stars++ // 成功
  } else if (random <= successRate + failRate) {
    // 失败处理（可能掉级）
  } else {
    // 装备损毁处理
  }
}
```

### 3. 纸娃娃模拟器

**涉及文件**:
- `app/paperdoll/page.tsx` - 纸娃娃模拟器

**功能特点**:
- 角色形象预览
- 装备分类展示（武器、防具、饰品、时装）
- 装备搭配功能
- 保存和重置搭配

### 4. 响应式导航系统

**涉及文件**:
- `components/header.tsx` - 顶部导航
- `components/sidebar.tsx` - 侧边栏

**功能特点**:
- 桌面端水平导航菜单
- 移动端折叠式菜单
- 搜索功能
- 通知和用户功能
- 实用站点链接

## 🔧 技术栈

### 核心框架
- **Next.js 14.2.16** - React 全栈框架，使用 App Router
- **React 18** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript

### UI 和样式
- **Tailwind CSS 3.4.17** - 原子化 CSS 框架
- **shadcn/ui** - 基于 Radix UI 的组件库
- **Lucide React** - 图标库
- **next-themes** - 主题切换支持

### 开发工具
- **ESLint** - 代码质量检查
- **PostCSS** - CSS 处理工具
- **pnpm** - 包管理器

### 构建配置
- **静态导出**: 配置为静态网站生成
- **图片优化**: 禁用 Next.js 图片优化以支持静态导出
- **路径别名**: 使用 `@/` 作为根目录别名

## 🚀 快速开始

### 环境要求
- Node.js 18+
- pnpm (推荐) 或 npm

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
pnpm dev
```

### 构建项目
```bash
pnpm build
```

### 添加新道具
1. 将道具图片放入 `public/images/cms-216/` 目录
2. 支持的格式：PNG, JPG, JPEG, GIF, WEBP
3. 文件名将自动转换为道具名称
4. 重新构建项目以生成静态页面

## 🚀 部署指南

### 静态部署 (推荐)

项目配置为静态导出，可部署到任何静态托管服务：

```bash
# 构建静态文件
pnpm build

# 输出目录: out/
# 将 out/ 目录内容上传到静态托管服务
```

### 支持的托管平台

1. **Vercel** (推荐)
   ```bash
   # 安装 Vercel CLI
   npm i -g vercel

   # 部署
   vercel --prod
   ```

2. **Netlify**
   - 连接 GitHub 仓库
   - 构建命令: `pnpm build`
   - 发布目录: `out`

3. **GitHub Pages**
   ```yaml
   # .github/workflows/deploy.yml
   name: Deploy to GitHub Pages
   on:
     push:
       branches: [ main ]
   jobs:
     build-and-deploy:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v2
         - uses: actions/setup-node@v2
           with:
             node-version: '18'
         - run: npm install
         - run: npm run build
         - uses: peaceiris/actions-gh-pages@v3
           with:
             github_token: ${{ secrets.GITHUB_TOKEN }}
             publish_dir: ./out
   ```

4. **自定义服务器**
   ```bash
   # 使用 nginx 配置
   server {
     listen 80;
     server_name your-domain.com;
     root /path/to/out;
     index index.html;

     location / {
       try_files $uri $uri/ $uri.html =404;
     }
   }
   ```

### 环境变量配置

创建 `.env.local` 文件（如需要）：

```env
# 网站配置
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_SITE_NAME=冒险岛情报站

# 分析工具（可选）
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

## 📁 数据管理

### 道具数据
- **图片存储**: `public/images/cms-216/`
- **数据生成**: 通过文件系统自动读取
- **命名规则**: 文件名转换为显示名称（下划线转空格，首字母大写）

### 脚本工具
- `scripts/gen-json-cms-216.js` - 生成道具 JSON 数据
- `scripts/build-test.js` - 构建测试脚本
- `scripts/final-build-verification.js` - 构建验证脚本

## 📡 API 文档

### 内部 API 函数

#### getCMS216Items()

读取 CMS-216 版本道具数据的服务端函数。

```typescript
// app/cms-216/page.tsx
async function getCMS216Items(): Promise<ItemData[]> {
  // 返回道具数组
}

interface ItemData {
  id: string        // 道具唯一标识
  name: string      // 道具显示名称
  image: string     // 图片路径
  filename: string  // 原始文件名
}
```

**使用示例**:
```typescript
const items = await getCMS216Items()
// 返回: [{ id: "dragon_sword", name: "Dragon Sword", ... }]
```

#### 星之力计算函数

```typescript
// app/starforce/page.tsx
interface StarforceData {
  success: number   // 成功概率 (%)
  fail: number      // 失败概率 (%)
  destroy: number   // 损毁概率 (%)
  cost: number      // 强化费用
}

const starforceData: Record<number, StarforceData> = {
  0: { success: 95, fail: 5, destroy: 0, cost: 1000 },
  // ... 更多星级数据
}
```

### 数据结构规范

#### 道具数据结构

```typescript
interface Item {
  id: string
  name: string
  type: string
  level: number
  description?: string
  stats?: {
    attack?: number
    str?: number
    dex?: number
    int?: number
    luk?: number
  }
  image: string
  rarity?: 'common' | 'rare' | 'epic' | 'legendary'
}
```

#### 强化结果数据

```typescript
interface SimulationResult {
  attempts: number      // 尝试次数
  totalCost: number     // 总花费
  success: boolean      // 是否成功
  finalStars: number    // 最终星数
}
```

### 文件系统 API

#### 图片资源路径规范

```
public/images/
├── cms-216/          # CMS-216 版本道具
├── equip-enchant/    # 装备强化相关
├── Screenshots/      # 截图资源
└── UIEquipEnchant/   # UI 相关图片
```

#### 数据文件路径

```
public/data/
├── items/            # 道具数据
├── equip-enchant/    # 强化数据
└── ...               # 其他数据类别
```

## 🎨 设计系统

### 颜色主题
- 主色调：蓝色到紫色渐变
- 背景：浅色渐变 (blue-50 → indigo-50 → purple-50)
- 卡片：白色半透明背景 + 毛玻璃效果

### 组件规范
- 统一使用 shadcn/ui 组件
- 响应式设计原则
- 一致的间距和圆角
- 平滑的过渡动画

## 🛠️ 开发指南

### 添加新功能页面

1. 在 `app/` 目录下创建新的页面文件夹
2. 创建 `page.tsx` 文件作为页面组件
3. 在 `components/header.tsx` 中添加导航链接
4. 更新首页 `app/page.tsx` 中的工具列表

### 组件开发规范

```typescript
// 组件文件结构示例
"use client" // 如果需要客户端功能

import { ComponentProps } from "@/components/ui/component"

interface MyComponentProps {
  // 定义 props 类型
}

export function MyComponent({ ...props }: MyComponentProps) {
  return (
    // JSX 内容
  )
}
```

### 样式指南

- 使用 Tailwind CSS 类名
- 保持一致的间距：`space-y-6`, `gap-4`
- 使用半透明背景：`bg-white/80 backdrop-blur-sm`
- 统一的圆角：`rounded-lg`
- 过渡动画：`transition-all duration-300`

### 数据处理

#### 添加新的道具类别

1. 在 `public/images/` 下创建新的类别文件夹
2. 参考 `app/cms-216/page.tsx` 创建读取逻辑
3. 实现类似的 `getItems()` 函数

#### 静态数据生成

```javascript
// scripts/gen-data.js 示例
const fs = require('fs')
const path = require('path')

function generateItemData(category) {
  const itemsDir = path.join(process.cwd(), `public/images/${category}`)
  const files = fs.readdirSync(itemsDir)

  return files.map(file => ({
    id: file.replace(/\.[^/.]+$/, ''),
    name: formatName(file),
    image: `/images/${category}/${file}`
  }))
}
```

## 🧪 测试

### 构建测试

项目包含多个构建测试脚本：

```bash
# 基础构建测试
node scripts/build-test.js

# 静态生成测试
node scripts/ssg-build-test.js

# 最终构建验证
node scripts/final-build-verification.js
```

### 手动测试清单

- [ ] 首页加载正常
- [ ] 导航菜单功能正常
- [ ] 道具列表显示正确
- [ ] 道具详情页面可访问
- [ ] 星之力模拟器计算正确
- [ ] 响应式布局在移动端正常
- [ ] 图片加载和错误处理

## 📊 性能优化

### 已实现的优化

1. **静态生成**: 使用 Next.js SSG 预生成页面
2. **图片优化**: 懒加载和错误处理
3. **代码分割**: Next.js 自动代码分割
4. **CSS 优化**: Tailwind CSS 的 purge 功能

### 建议的优化

1. **图片压缩**: 使用 WebP 格式
2. **缓存策略**: 设置适当的缓存头
3. **CDN 部署**: 使用 CDN 加速静态资源
4. **预加载**: 关键资源预加载

## 🔧 故障排除

### 常见问题

#### 1. 道具图片不显示
- 检查图片文件是否存在于 `public/images/cms-216/` 目录
- 确认图片格式是否支持 (PNG, JPG, JPEG, GIF, WEBP)
- 检查文件名是否包含特殊字符

#### 2. 构建失败
- 运行 `pnpm install` 重新安装依赖
- 检查 TypeScript 错误
- 确认所有必需的环境变量已设置

#### 3. 样式不生效
- 确认 Tailwind CSS 配置正确
- 检查类名是否拼写正确
- 运行 `pnpm dev` 重启开发服务器

### 调试技巧

```bash
# 查看构建详细信息
npm run build -- --debug

# 分析包大小
npm install -g @next/bundle-analyzer
ANALYZE=true npm run build
```

## 🔮 未来规划

### 短期目标 (1-3 个月)
- [ ] 完善星之力模拟器算法
- [ ] 添加更多道具类别
- [ ] 优化移动端体验
- [ ] 添加搜索功能

### 中期目标 (3-6 个月)
- [ ] 实现用户系统
- [ ] 添加收藏和分享功能
- [ ] 集成更多游戏工具
- [ ] 数据库 API 开发

### 长期目标 (6+ 个月)
- [ ] 多语言支持
- [ ] PWA 功能
- [ ] 实时数据同步
- [ ] 社区功能

## 🤝 贡献指南

### 如何贡献

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 保持代码简洁和可读性
- 添加适当的注释

## 📄 许可证

本项目仅供学习和研究使用。所有游戏相关内容版权归 Nexon 所有。

---

**联系方式**: 小帽子：499151029
**网站地址**: Mxd.dvg.cn
**更新日期**: 2025-06-19
