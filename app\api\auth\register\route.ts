import { NextRequest, NextResponse } from 'next/server'
import { hash } from 'bcryptjs'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { sendVerificationEmail } from '@/lib/email'
import { generateVerificationToken } from '@/lib/tokens'

const registerSchema = z.object({
  name: z.string().min(2).max(50),
  email: z.string().email(),
  password: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, password } = registerSchema.parse(body)

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 400 }
      )
    }

    // 加密密码
    const hashedPassword = await hash(password, 12)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        email,
        hashedPassword,
        isActive: true,
        emailVerified: null // 需要邮箱验证
      }
    })

    // 为新用户分配注册用户角色
    const registeredRole = await prisma.role.findUnique({
      where: { name: 'registered' }
    })

    if (registeredRole) {
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: registeredRole.id,
          grantedAt: new Date()
        }
      })
    }

    // 创建虚拟货币余额
    await prisma.currencyBalance.create({
      data: {
        userId: user.id,
        balance: 100, // 新用户赠送100欢乐豆
        frozenBalance: 0,
        totalEarned: 100,
        totalSpent: 0
      }
    })

    // 生成验证令牌并发送邮件
    const verificationToken = generateVerificationToken()
    
    // 存储验证令牌（这里简化处理，实际应该存储到数据库或Redis）
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: verificationToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
      }
    })

    // 发送验证邮件
    await sendVerificationEmail(email, verificationToken)

    return NextResponse.json({
      success: true,
      message: '注册成功，请查收验证邮件'
    })

  } catch (error) {
    console.error('Registration error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入数据格式错误' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    )
  }
}
