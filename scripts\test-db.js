const pgp = require('pg-promise')();

// 配置数据库连接
const db = pgp({
    host: 'ep-plain-fire-a120vngo-pooler.ap-southeast-1.aws.neon.tech',
    port: 5432,
    database: 'neondb',
    user: 'neondb_owner',
    password: 'npg_ko1rnU5dhRKb',
    // 可选：启用查询日志
    // query: e => console.log(e.query)
});

async function testPostgres() {
    try {
        console.log("✅ 开始测试 PostgreSQL 操作...");

        // 1️⃣ 创建 book 表
        const createTableQuery = `
            CREATE TABLE IF NOT EXISTS book (
                id SERIAL PRIMARY KEY,
                title TEXT NOT NULL,
                author TEXT NOT NULL,
                published_date DATE
            );
        `;
        await timeQuery(db.none(createTableQuery), "创建表 book");

        // 2️⃣ 清空表（避免重复插入）
        await timeQuery(db.none('DELETE FROM book'), "清空表 book");

        // 3️⃣ 插入模拟数据
        const insertQuery = `
            INSERT INTO book (title, author, published_date)
            VALUES 
                ('The Great Gatsby', '<PERSON><PERSON>', '1925-04-10'),
                ('To Kill a Mockingbird', 'Harper Lee', '1960-07-11'),
                ('1984', 'George Orwell', '1949-06-08');
        `;
        await timeQuery(db.none(insertQuery), "插入模拟书籍数据");

        // 4️⃣ 查询所有书籍
        const selectAllQuery = 'SELECT * FROM book';
        const books = await timeQuery(db.any(selectAllQuery), "查询所有书籍");
        console.log("📚 查询结果：", books);

        // 5️⃣ 查询特定书籍
        const selectOneQuery = 'SELECT * FROM book WHERE author = $1';
        const orwellBooks = await timeQuery(db.any(selectOneQuery, ['George Orwell']), "查询 George Orwell 的书");
        console.log("🔍 查询 George Orwell 的书：", orwellBooks);

        // 6️⃣ 更新一条记录
        const updateQuery = 'UPDATE book SET title = $1 WHERE title = $2 RETURNING *';
        const updatedBook = await timeQuery(db.one(updateQuery, ['1984 Revised Edition', '1984']), "更新书籍标题");
        console.log("✏️ 更新后的书籍：", updatedBook);

        // 7️⃣ 删除一本书
        const deleteQuery = 'DELETE FROM book WHERE title = $1 RETURNING *';
        const deletedBook = await timeQuery(db.one(deleteQuery, ['The Great Gatsby']), "删除 The Great Gatsby");
        console.log("🗑️ 被删除的书籍：", deletedBook);

    } catch (error) {
        console.error("❌ 出现错误：", error.message || error);
    } finally {
        pgp.end(); // 关闭连接
    }
}

// 🕒 封装一个方法来测量 SQL 执行时间
async function timeQuery(promise, label = '未知SQL') {
    const start = process.hrtime();
    try {
        const result = await promise;
        const elapsed = getElapsedTime(start);
        console.log(`✔️ ${label} 成功，耗时 ${elapsed} ms`);
        return result;
    } catch (err) {
        const elapsed = getElapsedTime(start);
        console.error(`❌ ${label} 失败，耗时 ${elapsed} ms`);
        throw err;
    }
}

function getElapsedTime(start) {
    const diff = process.hrtime(start);
    return (diff[0] * 1e3 + diff[1] / 1e6).toFixed(2);
}

// 🔍 启动测试
testPostgres();



