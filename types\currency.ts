import { Transaction, TransactionType, TransactionStatus } from '@prisma/client'

// 虚拟货币余额类型
export interface CurrencyBalanceData {
  balance: number
  frozenBalance: number
  totalEarned: number
  totalSpent: number
  availableBalance: number // balance - frozenBalance
}

// 交易请求类型
export interface TransactionRequest {
  type: TransactionType
  amount: number
  description: string
  metadata?: Record<string, any>
}

// 交易响应类型
export interface TransactionResponse {
  success: boolean
  transaction?: Transaction
  newBalance?: number
  message?: string
  error?: string
}

// 消费请求类型
export interface ConsumeRequest {
  amount: number
  description: string
  itemId?: string
  itemName?: string
  metadata?: Record<string, any>
}

// 充值请求类型
export interface RechargeRequest {
  amount: number
  paymentMethod: 'stripe' | 'alipay' | 'wechat'
  paymentIntentId?: string
  metadata?: Record<string, any>
}

// 交易历史查询参数
export interface TransactionQuery {
  page?: number
  limit?: number
  type?: TransactionType
  status?: TransactionStatus
  startDate?: Date
  endDate?: Date
  minAmount?: number
  maxAmount?: number
}

// 交易统计类型
export interface TransactionStats {
  totalTransactions: number
  totalAmount: number
  totalRecharge: number
  totalConsume: number
  totalReward: number
  averageAmount: number
  transactionsByType: Record<TransactionType, number>
  transactionsByStatus: Record<TransactionStatus, number>
}

// 货币配置类型
export interface CurrencyConfig {
  name: string // 货币名称，如"欢乐豆"
  symbol: string // 货币符号
  defaultAmount: number // 新用户默认金额
  minRecharge: number // 最小充值金额
  maxRecharge: number // 最大充值金额
  minConsume: number // 最小消费金额
  maxDailyConsume: number // 每日最大消费限额
  exchangeRate: number // 与真实货币的汇率
}

// 安全检查结果类型
export interface SecurityCheckResult {
  passed: boolean
  reason?: string
  riskLevel: 'low' | 'medium' | 'high'
  suggestions?: string[]
}

// 余额变动通知类型
export interface BalanceChangeNotification {
  userId: string
  type: 'increase' | 'decrease'
  amount: number
  newBalance: number
  reason: string
  timestamp: Date
}
