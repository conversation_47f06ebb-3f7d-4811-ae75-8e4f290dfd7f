import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

// 角色权限配置
const ROLE_PERMISSIONS = {
  guest: [
    'read:public',
  ],
  
  registered: [
    'read:public',
    'read:basic',
    'use:simulator',
    'save:config',
  ],
  
  vip: [
    'read:public',
    'read:basic',
    'use:simulator',
    'save:config',
    'export:data',
    'use:advanced_features',
    'unlimited:access',
    'priority:support',
  ],
  
  diamond: [
    'read:public',
    'read:basic',
    'use:simulator',
    'save:config',
    'export:data',
    'use:advanced_features',
    'unlimited:access',
    'priority:support',
  ],
  
  admin: [
    'read:public',
    'read:basic',
    'use:simulator',
    'save:config',
    'export:data',
    'use:advanced_features',
    'unlimited:access',
    'priority:support',
    'manage:users',
    'manage:content',
    'view:analytics',
  ],
  
  super_admin: [
    'read:public',
    'read:basic',
    'use:simulator',
    'save:config',
    'export:data',
    'use:advanced_features',
    'unlimited:access',
    'priority:support',
    'manage:users',
    'manage:content',
    'view:analytics',
    'system:config',
  ]
}

async function main() {
  console.log('开始初始化数据库...')

  // 创建角色
  console.log('创建角色...')
  for (const [roleName, permissions] of Object.entries(ROLE_PERMISSIONS)) {
    await prisma.role.upsert({
      where: { name: roleName },
      update: {
        permissions: permissions,
        updatedAt: new Date()
      },
      create: {
        name: roleName,
        displayName: getRoleDisplayName(roleName),
        description: getRoleDescription(roleName),
        permissions: permissions,
        isActive: true
      }
    })
    console.log(`✓ 角色 ${roleName} 创建完成`)
  }

  // 创建管理员用户
  console.log('创建管理员用户...')
  const adminPassword = await hash('admin123456', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '系统管理员',
      hashedPassword: adminPassword,
      emailVerified: new Date(),
      isActive: true
    }
  })

  // 为管理员分配角色
  const adminRole = await prisma.role.findUnique({
    where: { name: 'admin' }
  })

  if (adminRole) {
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: adminUser.id,
          roleId: adminRole.id
        }
      },
      update: {},
      create: {
        userId: adminUser.id,
        roleId: adminRole.id,
        grantedAt: new Date()
      }
    })
  }

  // 为管理员创建虚拟货币余额
  await prisma.currencyBalance.upsert({
    where: { userId: adminUser.id },
    update: {},
    create: {
      userId: adminUser.id,
      balance: 10000, // 给管理员10000欢乐豆
      frozenBalance: 0,
      totalEarned: 10000,
      totalSpent: 0
    }
  })

  console.log('✓ 管理员用户创建完成')

  // 创建系统配置
  console.log('创建系统配置...')
  const systemConfigs = [
    {
      key: 'currency_name',
      value: '欢乐豆',
      category: 'CURRENCY'
    },
    {
      key: 'default_currency_amount',
      value: 100,
      category: 'CURRENCY'
    },
    {
      key: 'max_daily_consume',
      value: 1000,
      category: 'CURRENCY'
    },
    {
      key: 'starforce_base_cost',
      value: 100,
      category: 'ENHANCEMENT'
    },
    {
      key: 'potential_base_cost',
      value: 50,
      category: 'ENHANCEMENT'
    },
    {
      key: 'additional_base_cost',
      value: 30,
      category: 'ENHANCEMENT'
    }
  ]

  for (const config of systemConfigs) {
    await prisma.systemConfig.upsert({
      where: { key: config.key },
      update: {
        value: config.value,
        updatedAt: new Date()
      },
      create: {
        key: config.key,
        value: config.value,
        category: config.category,
        isActive: true
      }
    })
  }

  console.log('✓ 系统配置创建完成')
  console.log('数据库初始化完成！')
}

function getRoleDisplayName(roleName: string): string {
  const displayNames: Record<string, string> = {
    guest: '游客用户',
    registered: '注册用户',
    vip: '黄金用户',
    diamond: '钻石用户',
    admin: '管理员',
    super_admin: '超级管理员'
  }
  return displayNames[roleName] || roleName
}

function getRoleDescription(roleName: string): string {
  const descriptions: Record<string, string> = {
    guest: '未注册的访客用户，功能受限',
    registered: '已注册的普通用户，可使用基础功能',
    vip: '黄金会员用户，享有高级功能权限',
    diamond: '钻石会员用户，享有最高级功能权限',
    admin: '系统管理员，拥有管理权限',
    super_admin: '超级管理员，拥有所有权限'
  }
  return descriptions[roleName] || ''
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
