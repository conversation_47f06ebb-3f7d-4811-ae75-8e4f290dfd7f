import NextAuth from "next-auth"
import { PrismaAdapter } from "@auth/prisma-adapter"
import CredentialsProvider from "next-auth/providers/credentials"
import { prisma } from "@/lib/db"
import { compare } from "bcryptjs"
import type { ExtendedUser, SessionUser } from "@/types/auth"

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma) as any,
  
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  
  jwt: {
    maxAge: 60 * 60 * 24 * 30, // 30 days
    secret: process.env.NEXTAUTH_SECRET,
  },
  
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("邮箱和密码不能为空")
        }

        // 查找用户
        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          include: {
            userRoles: {
              include: {
                role: true
              }
            },
            currencyBalance: true
          }
        })

        if (!user || !user.hashedPassword) {
          throw new Error("用户不存在或密码错误")
        }

        // 验证密码
        const isPasswordValid = await compare(credentials.password, user.hashedPassword)
        if (!isPasswordValid) {
          throw new Error("用户不存在或密码错误")
        }

        // 检查账户状态
        if (!user.isActive) {
          throw new Error("账户已被禁用")
        }

        if (!user.emailVerified) {
          throw new Error("请先验证邮箱")
        }

        // 更新最后登录时间
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLoginAt: new Date() }
        })

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          roles: user.userRoles.map(ur => ur.role.name),
          emailVerified: user.emailVerified
        }
      }
    })
  ],
  
  callbacks: {
    async jwt({ token, user, account }) {
      // 首次登录时添加用户信息
      if (user) {
        token.userId = user.id
        token.email = user.email
        token.name = user.name
        token.avatar = user.avatar
        token.roles = await getUserRoles(user.id)
        token.permissions = await getUserPermissions(user.id)
      }
      
      return token
    },
    
    async session({ session, token }) {
      // 将token信息传递给session
      if (token) {
        session.user = {
          id: token.userId as string,
          email: token.email as string,
          name: token.name as string,
          avatar: token.avatar as string,
          roles: token.roles as string[],
          permissions: token.permissions as string[],
          membershipLevel: getMembershipLevel(token.roles as string[])
        } as SessionUser
      }
      
      return session
    }
  },
  
  pages: {
    signIn: '/login',
    signUp: '/register',
    error: '/auth/error',
  },
  
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // 登录事件处理
      console.log('User signed in:', user.email)
    },
    
    async signOut({ session, token }) {
      // 登出事件处理
      console.log('User signed out')
    }
  }
})

// 导出 authOptions 以保持向后兼容
export const authOptions = {
  adapter: PrismaAdapter(prisma) as any,
  session: { strategy: "jwt" as const },
  providers: [],
  callbacks: {},
  pages: {
    signIn: '/login',
    signUp: '/register',
    error: '/auth/error',
  }
}

// 获取用户角色
async function getUserRoles(userId: string): Promise<string[]> {
  const userRoles = await prisma.userRole.findMany({
    where: { 
      userId,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ]
    },
    include: { role: true }
  })
  
  return userRoles.map(ur => ur.role.name)
}

// 获取用户权限
async function getUserPermissions(userId: string): Promise<string[]> {
  const roles = await getUserRoles(userId)
  const permissions = new Set<string>()
  
  for (const roleName of roles) {
    const role = await prisma.role.findUnique({
      where: { name: roleName }
    })
    
    if (role && role.permissions) {
      const rolePermissions = role.permissions as string[]
      rolePermissions.forEach(permission => permissions.add(permission))
    }
  }
  
  return Array.from(permissions)
}

// 获取会员等级
function getMembershipLevel(roles: string[]): SessionUser['membershipLevel'] {
  if (roles.includes('admin') || roles.includes('super_admin')) return 'admin'
  if (roles.includes('diamond')) return 'diamond'
  if (roles.includes('vip')) return 'vip'
  if (roles.includes('registered')) return 'registered'
  return 'guest'
}
