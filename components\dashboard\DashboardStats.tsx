'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Coins, TrendingUp, Activity, Zap } from 'lucide-react'

interface DashboardStatsProps {
  balance: number
  totalTransactions: number
  totalAmount: number
  totalEnhancements: number
}

export function DashboardStats({
  balance,
  totalTransactions,
  totalAmount,
  totalEnhancements
}: DashboardStatsProps) {
  const stats = [
    {
      title: '欢乐豆余额',
      value: balance.toLocaleString(),
      icon: Coins,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      description: '当前可用余额'
    },
    {
      title: '总交易次数',
      value: totalTransactions.toLocaleString(),
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: '累计交易记录'
    },
    {
      title: '总交易金额',
      value: totalAmount.toLocaleString(),
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: '累计交易金额'
    },
    {
      title: '强化次数',
      value: totalEnhancements.toLocaleString(),
      icon: Zap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: '装备强化次数'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {stat.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
