import { Metadata } from 'next'
import { EmailVerification } from '@/components/auth/EmailVerification'

export const metadata: Metadata = {
  title: '邮箱验证 - 冒险岛情报站',
  description: '验证您的邮箱地址',
}

interface VerifyEmailPageProps {
  searchParams: {
    token?: string
    email?: string
  }
}

export default function VerifyEmailPage({ searchParams }: VerifyEmailPageProps) {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-lg shadow-lg p-8">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          邮箱验证
        </h2>
        <p className="text-gray-600">
          正在验证您的邮箱地址...
        </p>
      </div>
      
      <EmailVerification 
        token={searchParams.token}
        email={searchParams.email}
      />
    </div>
  )
}
