'use client'

import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { UserPlus, LogIn, Sparkles, Crown, Star } from 'lucide-react'
import type { Session } from 'next-auth'

export function AuthSection() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="w-full max-w-md mx-auto">
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (session) {
    // 已登录用户显示欢迎信息
    return (
      <div className="w-full max-w-md mx-auto">
        <Card className="bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Crown className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-xl text-gray-900">
              欢迎回来，{session.user?.name}！
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              您已成功登录，现在可以享受完整的功能体验
            </p>
            
            <div className="space-y-2">
              <Button asChild className="w-full">
                <Link href="/dashboard">
                  <Sparkles className="w-4 h-4 mr-2" />
                  进入仪表板
                </Link>
              </Button>
              
              <Button variant="outline" asChild className="w-full">
                <Link href="/tools">
                  开始使用工具
                </Link>
              </Button>
            </div>

            <div className="pt-4 border-t border-blue-200">
              <p className="text-sm text-gray-500">
                会员等级：
                <span className="ml-1 font-medium text-blue-600">
                  {session.user.membershipLevel === 'diamond' && '💎 钻石用户'}
                  {session.user.membershipLevel === 'vip' && '⭐ 黄金用户'}
                  {session.user.membershipLevel === 'registered' && '👤 注册用户'}
                  {session.user.membershipLevel === 'admin' && '🛡️ 管理员'}
                  {session.user.membershipLevel === 'guest' && '👋 游客'}
                </span>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 未登录用户显示注册/登录选项
  return (
    <div className="w-full max-w-md mx-auto">
      <Card className="bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-200">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Star className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-xl text-gray-900">
            加入冒险岛情报站
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-gray-600">
            注册账户，解锁更多功能和专属内容
          </p>
          
          <div className="space-y-3">
            <Button asChild className="w-full">
              <Link href="/register">
                <UserPlus className="w-4 h-4 mr-2" />
                立即注册
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/login">
                <LogIn className="w-4 h-4 mr-2" />
                已有账户？登录
              </Link>
            </Button>
          </div>

          <div className="pt-4 border-t border-indigo-200">
            <h4 className="font-medium text-gray-900 mb-2 text-center">
              注册即可享受
            </h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2" />
                🎁 100欢乐豆新手奖励
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2" />
                🛠️ 完整的装备强化模拟器
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2" />
                📊 个人数据统计和保存
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2" />
                ⭐ 个性化设置和收藏
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
