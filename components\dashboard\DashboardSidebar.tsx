'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { 
  LayoutDashboard, 
  User, 
  CreditCard, 
  Settings, 
  History,
  Coins,
  Shield,
  BarChart3
} from 'lucide-react'
import { SessionUser } from '@/types/auth'

interface DashboardSidebarProps {
  user: SessionUser
}

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  description?: string
  requiredPermissions?: string[]
  adminOnly?: boolean
}

export function DashboardSidebar({ user }: DashboardSidebarProps) {
  const pathname = usePathname()

  const navItems: NavItem[] = [
    {
      title: '仪表板',
      href: '/dashboard',
      icon: LayoutDashboard,
      description: '概览和统计'
    },
    {
      title: '个人资料',
      href: '/profile',
      icon: User,
      description: '管理个人信息'
    },
    {
      title: '虚拟货币',
      href: '/currency',
      icon: Coins,
      description: '欢乐豆管理'
    },
    {
      title: '交易记录',
      href: '/transactions',
      icon: History,
      description: '查看交易历史'
    },
    {
      title: '充值中心',
      href: '/recharge',
      icon: CreditCard,
      description: '购买欢乐豆'
    },
    {
      title: '设置',
      href: '/settings',
      icon: Settings,
      description: '账户设置'
    }
  ]

  // 管理员专用导航项
  const adminNavItems: NavItem[] = [
    {
      title: '用户管理',
      href: '/admin/users',
      icon: Shield,
      description: '管理用户账户',
      adminOnly: true
    },
    {
      title: '数据分析',
      href: '/admin/analytics',
      icon: BarChart3,
      description: '查看系统统计',
      adminOnly: true
    }
  ]

  // 检查用户权限
  const hasPermission = (requiredPermissions?: string[]) => {
    if (!requiredPermissions) return true
    return requiredPermissions.some(permission => 
      user.permissions?.includes(permission)
    )
  }

  const isAdmin = user.membershipLevel === 'admin'

  // 过滤导航项
  const filteredNavItems = navItems.filter(item => hasPermission(item.requiredPermissions))
  const filteredAdminItems = isAdmin ? adminNavItems : []

  return (
    <aside className="w-64 bg-white border-r border-gray-200 min-h-screen">
      <div className="p-6">
        <div className="space-y-1">
          {/* 主要导航 */}
          <div className="space-y-1">
            {filteredNavItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href
              
              return (
                <Button
                  key={item.href}
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start h-auto p-3",
                    isActive && "bg-blue-50 text-blue-700 border-blue-200"
                  )}
                  asChild
                >
                  <Link href={item.href}>
                    <div className="flex items-center space-x-3">
                      <Icon className="h-5 w-5" />
                      <div className="flex flex-col items-start">
                        <span className="font-medium">{item.title}</span>
                        {item.description && (
                          <span className="text-xs text-gray-500">
                            {item.description}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                </Button>
              )
            })}
          </div>

          {/* 管理员导航 */}
          {filteredAdminItems.length > 0 && (
            <>
              <div className="pt-6">
                <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  管理功能
                </h3>
              </div>
              <div className="space-y-1 mt-2">
                {filteredAdminItems.map((item) => {
                  const Icon = item.icon
                  const isActive = pathname === item.href
                  
                  return (
                    <Button
                      key={item.href}
                      variant={isActive ? "secondary" : "ghost"}
                      className={cn(
                        "w-full justify-start h-auto p-3",
                        isActive && "bg-red-50 text-red-700 border-red-200"
                      )}
                      asChild
                    >
                      <Link href={item.href}>
                        <div className="flex items-center space-x-3">
                          <Icon className="h-5 w-5" />
                          <div className="flex flex-col items-start">
                            <span className="font-medium">{item.title}</span>
                            {item.description && (
                              <span className="text-xs text-gray-500">
                                {item.description}
                              </span>
                            )}
                          </div>
                        </div>
                      </Link>
                    </Button>
                  )
                })}
              </div>
            </>
          )}
        </div>

        {/* 用户信息卡片 */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">
                {user.name?.charAt(0)?.toUpperCase() || 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user.name}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user.email}
              </p>
            </div>
          </div>
          
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">会员等级</span>
              <span className="font-medium text-gray-900">
                {user.membershipLevel === 'diamond' && '钻石用户'}
                {user.membershipLevel === 'vip' && '黄金用户'}
                {user.membershipLevel === 'registered' && '注册用户'}
                {user.membershipLevel === 'admin' && '管理员'}
                {user.membershipLevel === 'guest' && '游客'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </aside>
  )
}
