import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { sendPasswordResetEmail } from '@/lib/email'
import { generateVerificationToken } from '@/lib/tokens'

const resetPasswordSchema = z.object({
  email: z.string().email()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = resetPasswordSchema.parse(body)

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email }
    })

    // 无论用户是否存在，都返回成功消息（安全考虑）
    if (!user) {
      return NextResponse.json({
        success: true,
        message: '如果该邮箱存在，重置链接已发送'
      })
    }

    // 检查用户是否激活
    if (!user.isActive) {
      return NextResponse.json({
        success: true,
        message: '如果该邮箱存在，重置链接已发送'
      })
    }

    // 生成重置令牌
    const resetToken = generateVerificationToken()
    
    // 删除该用户之前的重置令牌
    await prisma.verificationToken.deleteMany({
      where: {
        identifier: email,
        token: { startsWith: 'reset_' }
      }
    })

    // 存储重置令牌
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: `reset_${resetToken}`,
        expires: new Date(Date.now() + 60 * 60 * 1000) // 1小时后过期
      }
    })

    // 发送重置邮件
    await sendPasswordResetEmail(email, resetToken)

    return NextResponse.json({
      success: true,
      message: '如果该邮箱存在，重置链接已发送'
    })

  } catch (error) {
    console.error('Password reset error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '邮箱格式错误' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: '发送重置邮件失败，请稍后重试' },
      { status: 500 }
    )
  }
}
