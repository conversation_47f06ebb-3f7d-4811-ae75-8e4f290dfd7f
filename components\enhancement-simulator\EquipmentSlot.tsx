'use client'

import { useState } from 'react'
import { EquipmentSlotProps } from '@/types/enhancement'

const SLOT_IMAGES = {
  starforce: '/images/UIEquipEnchant/Main/Equip/Slot/Starforce.png',
  potential: '/images/UIEquipEnchant/Main/Equip/Slot/Potential.png',
  bonusstat: '/images/UIEquipEnchant/Main/Equip/Slot/Bonusstat.png',
}

const BADGE_IMAGES = {
  level0: '/images/UIEquipEnchant/Main/Equip/Badge/0.png',
  level1: '/images/UIEquipEnchant/Main/Equip/Badge/1.png',
  level2: '/images/UIEquipEnchant/Main/Equip/Badge/2.png',
}

function getBadgeImage(level: number): string {
  if (level >= 15) return BADGE_IMAGES.level2
  if (level >= 10) return BADGE_IMAGES.level1
  return BADGE_IMAGES.level0
}

export default function EquipmentSlot({
  equipment,
  enhancementType,
  level,
  onEquipmentSelect,
  onEquipmentClear,
  isEnhancing,
}: EquipmentSlotProps) {
  const [imageError, setImageError] = useState(false)
  
  const handleClick = () => {
    if (!isEnhancing) {
      onEquipmentSelect()
    }
  }
  
  const handleRightClick = (e: React.MouseEvent) => {
    e.preventDefault()
    if (!isEnhancing) {
      onEquipmentClear()
    }
  }
  
  const handleImageError = () => {
    setImageError(true)
  }
  
  const handleImageLoad = () => {
    setImageError(false)
  }

  return (
    <>
      {/* 装备槽背景 */}
      <div
        className={`
          absolute w-[90px] h-[90px] bg-cover bg-no-repeat cursor-pointer z-[2]
          ${isEnhancing ? 'cursor-not-allowed' : 'cursor-pointer'}
        `}
        style={{
          top: '168px',
          left: '209px',
          backgroundImage: `url(${SLOT_IMAGES[enhancementType]})`,
        }}
        onClick={handleClick}
        onContextMenu={handleRightClick}
      />
      
      {/* 装备图片或占位符 */}
      <div
        className="absolute w-[64px] h-[64px] z-[8] flex items-center justify-center"
        style={{
          top: '181px',
          left: '222px',
        }}
        onClick={handleClick}
        onContextMenu={handleRightClick}
      >
        {equipment.itemId ? (
          <>
            {!imageError ? (
              <img
                src={`/images/itempic/${equipment.itemId}.png`}
                alt={equipment.name}
                className="w-full h-full object-contain"
                onError={handleImageError}
                onLoad={handleImageLoad}
                draggable={false}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-red-400 text-2xl font-bold">
                ?
              </div>
            )}
          </>
        ) : (
          <div className="w-full h-full flex items-center justify-center border-2 border-dashed border-white/30 rounded-md text-white text-2xl">
            +
          </div>
        )}
      </div>
      
      {/* 装备等级徽章 */}
      <div
        className="absolute w-[53px] h-[35px] bg-cover bg-no-repeat flex items-center justify-center z-[3]"
        style={{
          top: '224px',
          right: '224px',
          backgroundImage: `url(${getBadgeImage(level)})`,
        }}
      >
        <span className="text-black text-[11px] font-bold mt-[-2px]">
          {level}
        </span>
      </div>
      
      {/* 通知框背景 */}
      <div
        className="absolute w-[443px] h-[30px] bg-cover bg-no-repeat z-[5]"
        style={{
          top: '116px',
          left: '32px',
          backgroundImage: 'url(/images/UIEquipEnchant/Main/Equip/Notice/Background.png)',
        }}
      />
      
      {/* 通知文本 */}
      <div
        className="absolute w-[443px] h-[30px] flex items-center justify-center z-[5] pointer-events-none"
        style={{
          top: '116px',
          left: '32px',
        }}
      >
        <span className="text-white text-[11px] text-center">
          {equipment.itemId ? (
            `${equipment.name} [${equipment.type}] (ID: ${equipment.itemId})`
          ) : (
            enhancementType === 'starforce' 
              ? '可将想要进行星力强化的装备拖放到Power Crystals上。'
              : enhancementType === 'potential'
              ? '潜能重设可以重新设定装备的潜在能力。'
              : '额外属性强化可以提升装备的额外能力。'
          )}
        </span>
      </div>
    </>
  )
}
