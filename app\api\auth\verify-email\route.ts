import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'

const verifyEmailSchema = z.object({
  token: z.string()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token } = verifyEmailSchema.parse(body)

    // 查找验证令牌
    const verificationToken = await prisma.verificationToken.findUnique({
      where: { token }
    })

    if (!verificationToken) {
      return NextResponse.json(
        { error: '验证令牌无效' },
        { status: 400 }
      )
    }

    // 检查令牌是否过期
    if (verificationToken.expires < new Date()) {
      // 删除过期令牌
      await prisma.verificationToken.delete({
        where: { token }
      })
      
      return NextResponse.json(
        { error: '验证令牌已过期' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email: verificationToken.identifier }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 400 }
      )
    }

    // 更新用户邮箱验证状态
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: new Date(),
        isActive: true
      }
    })

    // 删除已使用的验证令牌
    await prisma.verificationToken.delete({
      where: { token }
    })

    // 记录验证成功的交易（奖励）
    await prisma.transaction.create({
      data: {
        userId: user.id,
        type: 'REWARD',
        amount: 50, // 邮箱验证奖励50欢乐豆
        balanceBefore: 100,
        balanceAfter: 150,
        description: '邮箱验证奖励',
        status: 'COMPLETED',
        metadata: {
          reason: 'email_verification_reward'
        }
      }
    })

    // 更新用户余额
    await prisma.currencyBalance.update({
      where: { userId: user.id },
      data: {
        balance: { increment: 50 },
        totalEarned: { increment: 50 }
      }
    })

    return NextResponse.json({
      success: true,
      message: '邮箱验证成功'
    })

  } catch (error) {
    console.error('Email verification error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求数据格式错误' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: '邮箱验证失败，请稍后重试' },
      { status: 500 }
    )
  }
}
