import { Metadata } from 'next'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { DashboardStats } from '@/components/dashboard/DashboardStats'
import { RecentTransactions } from '@/components/dashboard/RecentTransactions'
import { MembershipCard } from '@/components/dashboard/MembershipCard'
import { QuickActions } from '@/components/dashboard/QuickActions'

export const metadata: Metadata = {
  title: '仪表板 - 冒险岛情报站',
  description: '查看您的账户概览和最新活动',
}

async function getUserDashboardData(userId: string) {
  // 获取用户基本信息
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      currencyBalance: true,
      userRoles: {
        include: { role: true }
      }
    }
  })

  // 获取最近交易记录
  const recentTransactions = await prisma.transaction.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    take: 5
  })

  // 获取统计数据
  const stats = await prisma.transaction.aggregate({
    where: { userId },
    _count: { id: true },
    _sum: { 
      amount: true 
    }
  })

  // 获取强化日志统计
  const enhancementStats = await prisma.enhancementLog.aggregate({
    where: { userId },
    _count: { id: true }
  })

  return {
    user,
    recentTransactions,
    totalTransactions: stats._count.id || 0,
    totalAmount: Number(stats._sum.amount) || 0,
    totalEnhancements: enhancementStats._count.id || 0
  }
}

export default async function DashboardPage() {
  const session = await auth()
  
  if (!session?.user?.id) {
    return <div>加载中...</div>
  }

  const dashboardData = await getUserDashboardData(session.user.id)

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          欢迎回来，{dashboardData.user?.name || '用户'}！
        </h1>
        <p className="text-gray-600 mt-2">
          这里是您的个人仪表板，查看最新的活动和统计数据
        </p>
      </div>

      {/* 会员卡片 */}
      <MembershipCard 
        user={dashboardData.user}
        membershipLevel={session.user.membershipLevel}
      />

      {/* 统计卡片 */}
      <DashboardStats
        balance={Number(dashboardData.user?.currencyBalance?.balance) || 0}
        totalTransactions={dashboardData.totalTransactions}
        totalAmount={dashboardData.totalAmount}
        totalEnhancements={dashboardData.totalEnhancements}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 快速操作 */}
        <div className="lg:col-span-1">
          <QuickActions membershipLevel={session.user.membershipLevel} />
        </div>

        {/* 最近交易 */}
        <div className="lg:col-span-2">
          <RecentTransactions transactions={dashboardData.recentTransactions} />
        </div>
      </div>
    </div>
  )
}
