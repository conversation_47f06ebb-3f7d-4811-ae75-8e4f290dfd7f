import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function DebugPage() {
  const envVars = [
    { name: 'DATABASE_URL', value: process.env.DATABASE_URL, required: true },
    { name: 'NEXTAUTH_URL', value: process.env.NEXTAUTH_URL, required: true },
    { name: 'NEXTAUTH_SECRET', value: process.env.NEXTAUTH_SECRET, required: true },
    { name: 'REDIS_URL', value: process.env.REDIS_URL, required: false },
    { name: 'RESEND_API_KEY', value: process.env.RESEND_API_KEY, required: false },
    { name: 'JWT_SECRET', value: process.env.JWT_SECRET, required: true },
  ]

  const getStatus = (value: string | undefined, required: boolean) => {
    if (!value) {
      return required ? 'error' : 'warning'
    }
    if (value.includes('your-') || value.includes('fallback')) {
      return 'warning'
    }
    return 'success'
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success': return '✅ 已配置'
      case 'warning': return '⚠️ 需要配置'
      case 'error': return '❌ 缺失'
      default: return '❓ 未知'
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>环境变量检查</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {envVars.map((env) => {
              const status = getStatus(env.value, env.required)
              return (
                <div key={env.name} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h3 className="font-medium">{env.name}</h3>
                    <p className="text-sm text-gray-500">
                      {env.required ? '必需' : '可选'}
                    </p>
                  </div>
                  <div className="text-right">
                    <Badge variant={status === 'success' ? 'default' : status === 'warning' ? 'secondary' : 'destructive'}>
                      {getStatusText(status)}
                    </Badge>
                    {env.value && (
                      <p className="text-xs text-gray-400 mt-1">
                        {env.value.substring(0, 20)}...
                      </p>
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">配置说明</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• DATABASE_URL: PostgreSQL 数据库连接字符串</li>
              <li>• NEXTAUTH_URL: 应用的基础 URL</li>
              <li>• NEXTAUTH_SECRET: NextAuth.js 的密钥</li>
              <li>• REDIS_URL: Redis 缓存连接 (可选)</li>
              <li>• RESEND_API_KEY: 邮件服务密钥 (可选)</li>
              <li>• JWT_SECRET: JWT 令牌密钥</li>
            </ul>
          </div>

          <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold text-yellow-900 mb-2">开发模式提示</h3>
            <p className="text-sm text-yellow-800">
              如果邮件服务未配置，验证链接将在控制台输出。
              请检查终端日志查看验证链接。
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
