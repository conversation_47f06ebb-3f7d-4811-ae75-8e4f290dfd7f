'use client'

import { LoadingScreenProps } from '@/types/enhancement'

export default function LoadingScreen({ isVisible, progress, status }: LoadingScreenProps) {
  if (!isVisible) return null

  return (
    <div
      className={`fixed top-0 left-0 w-screen h-screen bg-black/90 flex items-center justify-center z-[10000] transition-opacity duration-500 ${
        isVisible ? 'opacity-100 visible' : 'opacity-0 invisible'
      }`}
    >
      <div className="text-center">
        {/* 加载标题 */}
        <div className="mb-8">
          <h1 className="text-white text-3xl font-bold mb-2">
            装备强化模拟器
          </h1>
          <p className="text-gray-300 text-lg">
            MapleStory Universe Enhancement Simulator
          </p>
        </div>

        {/* 进度条容器 */}
        <div className="w-96 mx-auto mb-6">
          <div className="bg-gray-700 rounded-full h-4 overflow-hidden border border-gray-600">
            <div
              className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* 进度文本 */}
        <div className="text-center">
          <div className="text-white text-lg font-semibold mb-2">
            {status} {progress}%
          </div>
          <div className="text-gray-400 text-sm">
            正在加载资源，请稍候...
          </div>
        </div>

        {/* 加载动画 */}
        <div className="mt-8 flex justify-center">
          <div className="flex space-x-2">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"
                style={{
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: '1s'
                }}
              />
            ))}
          </div>
        </div>

        {/* 提示文字 */}
        <div className="mt-8 text-gray-500 text-sm max-w-md mx-auto">
          <p>首次加载可能需要较长时间，请耐心等待</p>
          <p className="mt-1">正在预加载游戏资源以提供最佳体验</p>
        </div>
      </div>
    </div>
  )
}
