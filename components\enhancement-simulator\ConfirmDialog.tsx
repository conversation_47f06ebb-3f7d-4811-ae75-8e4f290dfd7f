'use client'

import { ConfirmDialogProps } from '@/types/enhancement'

export default function ConfirmDialog({ isVisible, message, onConfirm, onCancel }: ConfirmDialogProps) {
  if (!isVisible) {
    return null
  }
  
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg p-8 max-w-md w-full mx-4">
        <h3 className="text-white text-xl font-bold text-center mb-6">
          确认强化
        </h3>
        
        <p className="text-gray-300 text-center mb-8">
          {message}
        </p>
        
        <div className="flex justify-center space-x-4">
          <button
            onClick={onCancel}
            className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors"
          >
            取消
          </button>
          <button
            onClick={onConfirm}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
          >
            确定
          </button>
        </div>
      </div>
    </div>
  )
}
