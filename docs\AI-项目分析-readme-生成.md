写一个AI编程工具的提示词。内容是：写出当前工作目录项目的详细的说明文档和开发文档

正常的流程应该是先把你的框架让AI写出详细的说明文档和开发文档
然后工具也要详细的说明文档和开发文档，让AI阅读两个文档，进行合并
AI读文件都为了省事只读一点点，所以代码量大一点的项目，AI是不懂装懂
但是通过说明文档可以让他不知道咋办的时候自己去文档里找


# 分析整个 MapleStory 信息站项目，并创建一个包含以下详细章节的综合 README.md 文件：

1. **项目结构分析**：
- 检查目录组织和文件夹层次结构
- 确定组件和页面的组织方式
- 分析所使用的模块化设计模式
- 记录代码库不同部分之间的关​​注点分离

2. **页面和路由分析**：
- 分析 pages 目录中的路由结构
- 确定项目使用的是 Next.js 应用路由器 (13+) 还是 Pages 路由器
- 记录所有动态路由（使用 [param] 语法）
- 记录所有嵌套路由模式
- 规划完整的路由结构

3. **功能实现分析**：
- 识别并记录所有主要功能模块（例如，用户身份验证、数据显示、搜索功能、详情页面等）
- 对于每个功能，描述：
- 涉及哪些页面和组件
- 数据流和状态管理方法
- 使用的 API 端点或数据源
- 使用的关键依赖项和库

4. **文档输出**：
- 在项目根目录中创建或更新 README.md 文件
- 使用清晰的标题和章节组织内容
- 在相关位置添加代码示例
- 提供清晰的概述，帮助新开发者快速理解项目

请首先探索代码库结构，然后系统地分析路由和功能，最后将所有发现整理成一个条理清晰的 README.md 文件。


# 基于你刚才完成的 MapleStory 信息站项目分析，现在需要将一个现有的 HTML 装备强化模拟器集成到当前的 Next.js 项目中。

**任务目标：**
将位于 `html/mxd-tools-enhancement` 目录的 MSU 装备强化模拟器完整迁移并集成到当前 Next.js 项目中。

**前置条件检查：**
1. 首先检查 `html/mxd-tools-enhancement` 目录是否存在且可访问
2. 查看该目录下的 README.md 文件了解项目详情
3. 分析现有 HTML/CSS/JavaScript 代码结构和功能
4. 确认图片资源是否位于 `public/assets/UIEquipEnchant/` 目录

**具体实施要求：**

1. **入口集成：**
    - 在现有的 `/tools` 页面中找到"装备工具栏"区域
    - 添加"装备强化模拟器"入口，保持与现有工具一致的视觉风格
    - 创建路由 `/tools/enhancement` 作为模拟器页面

2. **功能迁移：**
    - 完整保留原 HTML 版本的所有功能特性
    - 使用 React 组件化架构重构所有交互逻辑
    - 保持原有的用户界面布局和交互体验
    - 确保所有计算逻辑和数据处理准确迁移

3. **技术实现：**
    - 使用 TypeScript 进行类型安全开发
    - 采用 Tailwind CSS 进行样式实现
    - 遵循项目现有的组件结构和命名规范
    - 使用 shadcn/ui 组件库保持设计一致性

4. **代码组织：**
    - 主页面组件：`app/tools/enhancement/page.tsx`
    - 模拟器组件：`components/enhancement-simulator/` 目录
    - 类型定义：`types/enhancement.ts`
    - 工具函数：`lib/enhancement-utils.ts`
    - 静态资源：确认并使用 `public/assets/UIEquipEnchant/` 中的图片

5. **集成步骤：**
    - 分析原始代码的功能模块和数据结构
    - 设计 React 组件架构
    - 实现核心业务逻辑
    - 集成到现有项目结构
    - 测试功能完整性和用户体验

**输出要求：**
如果无法访问源代码目录，请明确说明并停止任务。
如果可以访问，请提供：
1. 源代码分析报告
2. 详细的迁移计划和步骤
3. 完整的代码实现
4. 集成测试建议

请按照这个顺序逐步执行，确保每个步骤都符合当前项目的架构和代码规范。




# 3

请你担任一位专业的技术文档工程师和资深开发者。
基于当前工作目录中的项目文件结构、源代码和配置文件内容，为我生成两部分文档：项目说明文档（README 级别）、开发文档（Developer Guide）

📘 项目说明文档（README 级别）
项目名称与简介
项目功能与特性
技术栈说明
依赖环境与安装步骤
启动与使用方式
示例截图或接口演示（如果能分析出来）

🧑‍💻 开发文档（Developer Guide）
项目目录结构说明
主要模块/组件功能与职责
配置文件作用说明
开发、调试、构建流程说明
接口或 API 说明（如有）
常见问题与调试建议

基于工作区中当前的 MapleStory 信息站项目，分析完整的代码库结构、源代码、配置文件和现有文档，生成两个全面的文档文件：
要求：使用中文生成文档。
1. **项目 README 文档** (`README.md`)：
- 项目概述和目的（此 MapleStory 工具/信息站的功能）
- 主要特性和功能
- 技术栈（Next.js、React、TypeScript、Tailwind CSS、shadcn/ui 等）
- 安装和设置说明
- 使用示例和屏幕截图（如适用）
- 项目结构概述
- 贡献指南
- 许可证信息

2. **开发者指南文档** (`DEVELOPER_GUIDE.md`)：
- 详细的项目架构和文件组织
- 所使用的代码结构和模式（尤其是工具集成模式：app/tools/[tool]/page.tsx、components/[tool]-simulator/ 等）
- 组件层次结构和关系
- 数据模型和类型（尤其是 MapleStory 相关类型）
- API端点和数据流
- 开发工作流程和最佳实践
- 测试方法和指南
- 构建和部署流程
- 常见问题排查
- 代码风格和规范

请彻底分析现有代码库，包括所有 React 组件、TypeScript 类型、实用函数、配置文件以及所有现有文档。确保文档准确反映项目的当前状态并遵循专业的文档标准。请重点关注 MapleStory 特有的功能和工具，因为它们似乎是此应用程序的核心目的。
