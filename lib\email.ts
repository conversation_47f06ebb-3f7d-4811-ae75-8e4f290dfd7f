import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY)

const FROM_EMAIL = process.env.EMAIL_FROM || '<EMAIL>'
const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME || '冒险岛情报站'
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'

export async function sendVerificationEmail(email: string, token: string) {
  const verificationUrl = `${APP_URL}/verify-email?token=${token}&email=${encodeURIComponent(email)}`

  // 如果没有配置 Resend API 密钥，只记录日志
  if (!process.env.RESEND_API_KEY || process.env.RESEND_API_KEY === 're_your-resend-api-key') {
    console.log('📧 邮件验证链接 (开发模式):', verificationUrl)
    console.log('📧 收件人:', email)
    return
  }

  try {
    await resend.emails.send({
      from: FROM_EMAIL,
      to: email,
      subject: `${APP_NAME} - 邮箱验证`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>邮箱验证</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">${APP_NAME}</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">您的专业冒险岛工具平台</p>
          </div>
          
          <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0;">欢迎加入${APP_NAME}！</h2>
            
            <p>感谢您注册${APP_NAME}账户。为了确保账户安全，请点击下方按钮验证您的邮箱地址：</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 5px; 
                        font-weight: bold;
                        display: inline-block;">
                验证邮箱地址
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px;">
              如果按钮无法点击，请复制以下链接到浏览器地址栏：<br>
              <a href="${verificationUrl}" style="color: #667eea; word-break: break-all;">${verificationUrl}</a>
            </p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #333;">验证后您将获得：</h3>
              <ul style="margin: 0; padding-left: 20px;">
                <li>🎁 50欢乐豆新手奖励</li>
                <li>🛠️ 完整的装备强化模拟器功能</li>
                <li>📊 数据导出和保存功能</li>
                <li>⭐ 个性化设置和收藏功能</li>
              </ul>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 30px;">
              此验证链接将在24小时后过期。如果您没有注册${APP_NAME}账户，请忽略此邮件。
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>© 2024 ${APP_NAME}. 保留所有权利。</p>
          </div>
        </body>
        </html>
      `
    })
  } catch (error) {
    console.error('Failed to send verification email:', error)
    throw new Error('发送验证邮件失败')
  }
}

export async function sendPasswordResetEmail(email: string, token: string) {
  const resetUrl = `${APP_URL}/reset-password/confirm?token=${token}&email=${encodeURIComponent(email)}`

  // 如果没有配置 Resend API 密钥，只记录日志
  if (!process.env.RESEND_API_KEY || process.env.RESEND_API_KEY === 're_your-resend-api-key') {
    console.log('📧 密码重置链接 (开发模式):', resetUrl)
    console.log('📧 收件人:', email)
    return
  }

  try {
    await resend.emails.send({
      from: FROM_EMAIL,
      to: email,
      subject: `${APP_NAME} - 密码重置`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>密码重置</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">${APP_NAME}</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">密码重置请求</p>
          </div>
          
          <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0;">重置您的密码</h2>
            
            <p>我们收到了您的密码重置请求。如果这是您本人操作，请点击下方按钮重置密码：</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 5px; 
                        font-weight: bold;
                        display: inline-block;">
                重置密码
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px;">
              如果按钮无法点击，请复制以下链接到浏览器地址栏：<br>
              <a href="${resetUrl}" style="color: #667eea; word-break: break-all;">${resetUrl}</a>
            </p>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p style="margin: 0; color: #856404;">
                <strong>安全提醒：</strong>此重置链接将在1小时后过期。如果您没有请求重置密码，请忽略此邮件，您的账户仍然安全。
              </p>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 30px;">
              如果您在使用过程中遇到任何问题，请联系我们的客服团队。
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>© 2024 ${APP_NAME}. 保留所有权利。</p>
          </div>
        </body>
        </html>
      `
    })
  } catch (error) {
    console.error('Failed to send password reset email:', error)
    throw new Error('发送重置邮件失败')
  }
}

export async function sendWelcomeEmail(email: string, name: string) {
  try {
    await resend.emails.send({
      from: FROM_EMAIL,
      to: email,
      subject: `欢迎加入${APP_NAME}！`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>欢迎加入</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">${APP_NAME}</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">欢迎加入我们的大家庭！</p>
          </div>
          
          <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0;">你好，${name}！</h2>
            
            <p>恭喜您成功加入${APP_NAME}！我们很高兴您选择我们作为您的冒险岛工具平台。</p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #333;">您现在可以享受：</h3>
              <ul style="margin: 0; padding-left: 20px;">
                <li>🛠️ 专业的装备强化模拟器</li>
                <li>📊 详细的数据分析和统计</li>
                <li>💰 虚拟货币系统（欢乐豆）</li>
                <li>⭐ 个性化设置和收藏功能</li>
                <li>🎁 定期活动和奖励</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${APP_URL}/dashboard" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 5px; 
                        font-weight: bold;
                        display: inline-block;">
                开始使用
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 30px;">
              如果您有任何问题或建议，随时联系我们。祝您使用愉快！
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>© 2024 ${APP_NAME}. 保留所有权利。</p>
          </div>
        </body>
        </html>
      `
    })
  } catch (error) {
    console.error('Failed to send welcome email:', error)
    // 欢迎邮件失败不应该阻止注册流程
  }
}
