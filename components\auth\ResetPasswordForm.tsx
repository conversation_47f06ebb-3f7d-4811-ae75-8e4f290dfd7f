'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, Mail } from 'lucide-react'

const resetPasswordSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址')
})

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

export function ResetPasswordForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<ResetPasswordFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(resetPasswordSchema)
  })

  const onSubmit = async (data: ResetPasswordFormData) => {
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: data.email })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '发送重置邮件失败')
      }

      setSuccess(true)
    } catch (error) {
      setError(error instanceof Error ? error.message : '发送重置邮件失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="relative">
            <Mail className="h-16 w-16 text-blue-500" />
            <CheckCircle className="h-6 w-6 text-green-500 absolute -top-1 -right-1 bg-white rounded-full" />
          </div>
        </div>
        <h3 className="text-lg font-semibold text-gray-900">邮件已发送</h3>
        <p className="text-gray-600">
          我们已向您的邮箱发送了密码重置链接，请查收邮件并按照说明操作。
        </p>
        <div className="text-sm text-gray-500 space-y-1">
          <p>如果您没有收到邮件，请检查：</p>
          <ul className="list-disc list-inside space-y-1">
            <li>邮箱地址是否正确</li>
            <li>垃圾邮件文件夹</li>
            <li>邮件可能需要几分钟才能到达</li>
          </ul>
        </div>
        <Button
          variant="outline"
          onClick={() => setSuccess(false)}
          className="mt-4"
        >
          重新发送
        </Button>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="email">邮箱地址</Label>
        <Input
          id="email"
          type="email"
          placeholder="请输入您的邮箱地址"
          {...register('email')}
          disabled={isLoading}
        />
        {errors.email && (
          <p className="text-sm text-red-600">{errors.email.message}</p>
        )}
        <p className="text-sm text-gray-500">
          我们将向此邮箱发送密码重置链接
        </p>
      </div>

      <Button
        type="submit"
        className="w-full"
        disabled={isLoading}
      >
        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        发送重置邮件
      </Button>
    </form>
  )
}
