# 1

在登录页面填入登录信息，点击登录，没有反应。 没有发起后台调用。
在注册页面填入注册信息，点击注册，也没有反应。没有发起后台调用。 页面出现红色字体"Expected boolean, received string"
`components/home/<USER>"Property 'membershipLevel' does not exist on type 'User'."
`components/header.tsx` 有个编译错误 "Property 'avatar' does not exist on type 'User'"
解决上面的这些问题

## 1.1
我在测试全栈Next.js架构升级后的认证功能时遇到了多个问题，需要你帮助解决：

**前端功能问题：**
1. **登录功能失效**：在 `/login` 页面填入正确的登录信息并点击"登录"按钮后，页面没有任何反应，浏览器开发者工具的Network标签页显示没有发起任何后台API调用
2. **注册功能失效**：在 `/register` 页面填入注册信息并点击"注册"按钮后，页面没有反应且没有发起后台API调用，同时页面显示红色错误信息："Expected boolean, received string"

**TypeScript编译错误：**
3. **AuthSection组件类型错误**：`components/home/<USER>"Property 'membershipLevel' does not exist on type 'User'."
4. **Header组件类型错误**：`components/header.tsx` 文件中出现编译错误："Property 'avatar' does not exist on type 'User'"，具体位置在 `<AvatarImage src={session.user?.avatar} alt={session.user?.name} />` 这行代码

**需要解决的具体任务：**
- 修复NextAuth.js v5的类型定义问题，确保User类型包含必要的属性（avatar, membershipLevel等）
- 检查并修复登录/注册表单的事件处理和API调用逻辑
- 解决Zod验证schema中可能存在的类型不匹配问题
- 确保所有认证相关的组件都能正常工作并与后端API正确通信

请按照问题的优先级顺序逐一解决，并提供具体的代码修改方案。